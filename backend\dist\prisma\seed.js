"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcrypt");
const prisma = new client_1.PrismaClient();
async function main() {
    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash('123456', salt);
    const admin = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            role: client_1.Role.Admin,
            email: '<EMAIL>',
            password: hashedPassword,
            needsVerification: false,
            isActive: true
        },
    });
    console.log('Admin seedé:', admin);
}
main()
    .then(() => console.log('✅ Seed terminé'))
    .catch((err) => console.error('❌ Erreur:', err))
    .finally(() => prisma.$disconnect());
//# sourceMappingURL=seed.js.map