"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const nestjs_prisma_1 = require("nestjs-prisma");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const programs_module_1 = require("./programs/programs.module");
const modules_module_1 = require("./modules/modules.module");
const courses_module_1 = require("./courses/courses.module");
const mail_module_1 = require("./mail/mail.module");
const contenu_module_1 = require("./contenu/contenu.module");
const buildProgram_module_1 = require("./buildProgram/buildProgram.module");
const quiz_module_1 = require("./quiz/quiz.module");
const session2_module_1 = require("./session2/session2.module");
const seance_formateur_module_1 = require("./seance-formateur/seance-formateur.module");
const feedback_module_1 = require("./feedback/feedback.module");
const chatbot_module_1 = require("./chatbot/chatbot.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [nestjs_prisma_1.PrismaModule.forRoot({ isGlobal: true }), auth_module_1.AuthModule, users_module_1.UsersModule, programs_module_1.ProgramsModule, modules_module_1.ModulesModule, mail_module_1.MailModule, courses_module_1.CoursesModule, contenu_module_1.ContenuModule, buildProgram_module_1.buildProgramModule, quiz_module_1.QuizModule, session2_module_1.Session2Module, seance_formateur_module_1.SeanceFormateurModule, feedback_module_1.FeedbackModule, chatbot_module_1.ChatbotModule,],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map