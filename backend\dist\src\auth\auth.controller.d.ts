import { Request } from 'express';
import { AuthService } from './auth.service';
import { RegisterDto, LoginDto, ChangePasswordDto } from './dto/create-auth.dto';
import { UpdateAuthDto } from './dto/update-auth.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(dto: LoginDto): Promise<{
        success: boolean;
        message: string;
        data: {
            rememberMe: boolean;
            access_token: string;
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string;
            about: string | null;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
    register(dto: RegisterDto): Promise<{
        success: boolean;
        message: string;
        data: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string;
            about: string | null;
            createdAt: Date;
            updatedAt: Date;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    }>;
    findAll(): Promise<{
        success: boolean;
        data: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string;
            about: string | null;
        }[];
    }>;
    findOne(id: number): Promise<{
        success: boolean;
        data: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string;
            about: string | null;
        };
    }>;
    update(id: number, updateAuthDto: UpdateAuthDto): Promise<{
        success: boolean;
        message: string;
        data: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string;
            about: string | null;
        };
    }>;
    remove(id: number): Promise<{
        success: boolean;
        message: string;
        data: {
            id: number;
        };
    }>;
    forgot(email: string): Promise<{
        success: boolean;
        message: string;
        data: {
            message: string;
        };
    }>;
    reset(token: string, newPass: string, confirmPass: string, request: Request): Promise<{
        success: boolean;
        message: string;
        data: {
            message: string;
        };
    }>;
    changePassword(changePasswordDto: ChangePasswordDto): Promise<{
        success: boolean;
        message: string;
        data: {
            message: string;
        };
    }>;
    logout(body?: any): Promise<{
        success: boolean;
        message: string;
        data: {
            loggedOut: boolean;
            timestamp: string;
        };
    }>;
}
