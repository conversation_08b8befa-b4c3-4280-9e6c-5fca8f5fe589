"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const create_auth_dto_1 = require("./dto/create-auth.dto");
const update_auth_dto_1 = require("./dto/update-auth.dto");
let AuthController = class AuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async verifyAccount(body) {
        try {
            const user = await this.authService.getUserByEmail(body.email);
            return {
                success: true,
                message: 'Compte vérifié avec succès',
                data: { user }
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur lors de la vérification du compte', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async login(dto) {
        try {
            console.log('Login request received:', {
                email: dto.email,
                rememberMe: dto.rememberMe,
            });
            const result = await this.authService.login(dto);
            return {
                success: true,
                message: 'Connexion réussie',
                data: {
                    ...result,
                    rememberMe: dto.rememberMe || false,
                    access_token: `temp_token_${Date.now()}_${dto.rememberMe ? 'long' : 'short'}`
                }
            };
        }
        catch (error) {
            console.error('Login error:', error);
            throw new common_1.HttpException(error.message || 'Échec de la connexion', error.status || common_1.HttpStatus.UNAUTHORIZED);
        }
    }
    async register(dto) {
        try {
            const user = await this.authService.register(dto);
            return { success: true, message: 'Utilisateur créé', data: user };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Échec de l’enregistrement', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async findAll() {
        try {
            const users = await this.authService.findAll();
            return { success: true, data: users };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur lors de la récupération', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOne(id) {
        try {
            const user = await this.authService.findOne(id);
            return { success: true, data: user };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Utilisateur non trouvé', error.status || common_1.HttpStatus.NOT_FOUND);
        }
    }
    async update(id, updateAuthDto) {
        try {
            const user = await this.authService.update(id, updateAuthDto);
            return { success: true, message: 'Utilisateur mis à jour', data: user };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur lors de la mise à jour', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async remove(id) {
        try {
            const result = await this.authService.remove(id);
            return { success: true, message: 'Utilisateur supprimé', data: result };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur lors de la suppression', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async forgot(email) {
        try {
            const result = await this.authService.forgotPassword(email);
            return { success: true, message: 'Email de réinitialisation envoyé', data: result };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur lors de la demande', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async reset(dto) {
        try {
            const result = await this.authService.resetPassword(dto.token, dto.newPass, dto.confirmPass);
            return { success: true, message: 'Mot de passe réinitialisé', data: result };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur de réinitialisation', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async changePassword(changePasswordDto) {
        try {
            const result = await this.authService.changePassword(changePasswordDto.email, changePasswordDto.currentPassword, changePasswordDto.newPassword);
            return { success: true, message: 'Mot de passe changé avec succès', data: result };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Erreur lors du changement de mot de passe', error.status || common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async logout(body) {
        try {
            console.log('Logout request received');
            return {
                success: true,
                message: 'Déconnexion réussie',
                data: {
                    loggedOut: true,
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            console.error('Logout error:', error);
            throw new common_1.HttpException(error.message || 'Erreur lors de la déconnexion', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('verify'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyAccount", null);
__decorate([
    (0, common_1.Post)('login'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_auth_dto_1.LoginDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_auth_dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_auth_dto_1.UpdateAuthDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('users/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "forgot", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_auth_dto_1.ResetPassword]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "reset", null);
__decorate([
    (0, common_1.Post)('change-password'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_auth_dto_1.ChangePasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
__decorate([
    (0, common_1.Post)('logout'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map