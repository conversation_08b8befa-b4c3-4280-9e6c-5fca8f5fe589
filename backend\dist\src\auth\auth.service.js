"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const bcrypt = require("bcrypt");
const crypto = require("crypto");
const mail_service_1 = require("../mail/mail.service");
let AuthService = class AuthService {
    prisma;
    mailService;
    constructor(prisma, mailService) {
        this.prisma = prisma;
        this.mailService = mailService;
    }
    async login(dto) {
        const user = await this.prisma.user.findUnique({
            where: { email: dto.email },
        });
        if (!user) {
            throw new common_1.HttpException('Invalid email', common_1.HttpStatus.BAD_REQUEST);
        }
        if (!(await bcrypt.compare(dto.password, user.password))) {
            throw new common_1.HttpException('Invalid password', common_1.HttpStatus.BAD_REQUEST);
        }
        const { password, resetToken, resetTokenExpiry, ...safeUser } = user;
        return safeUser;
    }
    async register(dto) {
        const existing = await this.prisma.user.findUnique({ where: { email: dto.email } });
        if (existing)
            throw new common_1.HttpException('Email already exists', common_1.HttpStatus.BAD_REQUEST);
        const hashedPassword = await bcrypt.hash(dto.password, 10);
        const user = await this.prisma.user.create({
            data: {
                email: dto.email,
                password: hashedPassword,
                role: dto.role,
                name: dto.name,
            },
        });
        const { password, ...result } = user;
        return result;
    }
    async findAll() {
        return this.prisma.user.findMany({
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
    }
    async findOne(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
        if (!user)
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        return user;
    }
    async update(id, updateData) {
        const user = await this.prisma.user.findUnique({ where: { id } });
        if (!user)
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        return this.prisma.user.update({
            where: { id },
            data: updateData,
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
    }
    async remove(id) {
        const user = await this.prisma.user.findUnique({ where: { id } });
        if (!user)
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        await this.prisma.user.delete({ where: { id } });
        return { id };
    }
    async getUserById(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
        if (!user)
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        return user;
    }
    async getUserByEmail(email) {
        const user = await this.prisma.user.findUnique({
            where: { email },
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
        if (!user)
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        return user;
    }
    async forgotPassword(email) {
        try {
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user)
                throw new common_1.HttpException('User not found', common_1.HttpStatus.BAD_REQUEST);
            const token = crypto.randomBytes(32).toString('hex');
            await this.prisma.user.update({
                where: { email },
                data: {
                    resetToken: token,
                    resetTokenExpiry: new Date(Date.now() + 1000 * 60 * 60),
                },
            });
            await this.mailService.sendPasswordResetEmail(email, token);
            return { message: 'Reset link sent' };
        }
        catch (error) {
            console.log(error);
            throw error;
        }
    }
    async resetPassword(token, newPass, confirmPass) {
        const user = await this.prisma.user.findFirst({
            where: {
                resetToken: token,
                resetTokenExpiry: { gte: new Date() },
            },
        });
        if (!user)
            throw new common_1.HttpException('Invalid or expired token', common_1.HttpStatus.BAD_REQUEST);
        if (newPass !== confirmPass)
            throw new common_1.HttpException('Passwords do not match', common_1.HttpStatus.BAD_REQUEST);
        const hashedNew = await bcrypt.hash(newPass, 10);
        await this.prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedNew,
                resetToken: null,
                resetTokenExpiry: null,
            },
        });
        return { message: 'Password reset successful' };
    }
    async changePassword(email, currentPassword, newPassword) {
        if (!email || !currentPassword || !newPassword) {
            throw new common_1.HttpException('All fields are required', common_1.HttpStatus.BAD_REQUEST);
        }
        if (newPassword.length < 6) {
            throw new common_1.HttpException('New password must be at least 6 characters long', common_1.HttpStatus.BAD_REQUEST);
        }
        const user = await this.prisma.user.findUnique({
            where: { email },
        });
        if (!user) {
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        }
        const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isPasswordValid) {
            throw new common_1.HttpException('Current password is incorrect', common_1.HttpStatus.BAD_REQUEST);
        }
        if (currentPassword === newPassword) {
            throw new common_1.HttpException('New password must be different from current password', common_1.HttpStatus.BAD_REQUEST);
        }
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await this.prisma.user.update({
            where: { email },
            data: {
                password: hashedPassword,
            },
        });
        return { message: 'Password changed successfully' };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService, mail_service_1.MailService])
], AuthService);
//# sourceMappingURL=auth.service.js.map