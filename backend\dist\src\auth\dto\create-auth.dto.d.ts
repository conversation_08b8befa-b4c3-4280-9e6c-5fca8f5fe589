import { Role } from '@prisma/client';
export declare class CreateAuthDto {
}
export declare class LoginDto {
    email: string;
    password: string;
    rememberMe?: boolean;
}
export declare class RegisterDto {
    email: string;
    password: string;
    role: Role;
    name?: string;
}
export declare class ChangePasswordDto {
    email: string;
    currentPassword: string;
    newPassword: string;
}
export declare class ResetPassword {
    token: string;
    newPass: string;
    confirmPass: string;
}
