import { buildProgramService } from './buildProgram.service';
export declare class buildProgramController {
    private readonly buildProgramService;
    constructor(buildProgramService: buildProgramService);
    create(file: Express.Multer.File, body: any): Promise<{
        message: string;
        buildProgramId: number;
    }>;
    findAll(): Promise<({
        program: {
            id: number;
            name: string;
            published: boolean;
        };
        modules: ({
            courses: ({
                contenus: ({
                    contenu: {
                        id: number;
                        type: import(".prisma/client").$Enums.ContenuType;
                        title: string;
                        published: boolean;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                    };
                } & {
                    id: number;
                    contenuId: number;
                    buildProgramCourseId: number;
                })[];
                course: {
                    id: number;
                    title: string;
                };
            } & {
                id: number;
                courseId: number;
                buildProgramModuleId: number;
            })[];
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
        } & {
            id: number;
            moduleId: number;
            buildProgramId: number;
        })[];
    } & {
        id: number;
        createdAt: Date | null;
        level: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    })[]>;
    remove(id: string): Promise<{
        id: number;
        createdAt: Date | null;
        level: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    }>;
    update(id: string, file: Express.Multer.File, body: any): Promise<{
        message: string;
    }>;
    getByProgram(programId: string): Promise<({
        program: {
            id: number;
            name: string;
            published: boolean;
        };
        modules: ({
            courses: ({
                contenus: ({
                    contenu: {
                        id: number;
                        type: import(".prisma/client").$Enums.ContenuType;
                        title: string;
                        published: boolean;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                    };
                } & {
                    id: number;
                    contenuId: number;
                    buildProgramCourseId: number;
                })[];
                course: {
                    id: number;
                    title: string;
                };
            } & {
                id: number;
                courseId: number;
                buildProgramModuleId: number;
            })[];
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
        } & {
            id: number;
            moduleId: number;
            buildProgramId: number;
        })[];
    } & {
        id: number;
        createdAt: Date | null;
        level: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    }) | null>;
}
