"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildProgramModule = void 0;
const common_1 = require("@nestjs/common");
const buildProgram_service_1 = require("./buildProgram.service");
const buildProgram_controller_1 = require("./buildProgram.controller");
let buildProgramModule = class buildProgramModule {
};
exports.buildProgramModule = buildProgramModule;
exports.buildProgramModule = buildProgramModule = __decorate([
    (0, common_1.Module)({
        controllers: [buildProgram_controller_1.buildProgramController],
        providers: [buildProgram_service_1.buildProgramService],
    })
], buildProgramModule);
//# sourceMappingURL=buildProgram.module.js.map