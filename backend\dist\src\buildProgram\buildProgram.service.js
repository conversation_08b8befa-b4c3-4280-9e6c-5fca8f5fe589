"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildProgramService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let buildProgramService = class buildProgramService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        const { programId, level, modules } = data;
        const parsedModules = JSON.parse(modules);
        const buildProgram = await this.prisma.buildProgram.create({
            data: {
                programId: Number(programId),
                level,
            },
        });
        for (const mod of parsedModules) {
            const buildProgramModule = await this.prisma.buildProgramModule.create({
                data: {
                    buildProgramId: buildProgram.id,
                    moduleId: mod.moduleId,
                },
            });
            for (const course of mod.courses) {
                const buildProgramCourse = await this.prisma.buildProgramCourse.create({
                    data: {
                        buildProgramModuleId: buildProgramModule.id,
                        courseId: course.courseId,
                    },
                });
                for (const contenu of course.contenus) {
                    await this.prisma.buildProgramContenu.create({
                        data: {
                            buildProgramCourseId: buildProgramCourse.id,
                            contenuId: contenu.contenuId,
                        },
                    });
                }
            }
        }
        return { message: 'Program créée avec succès ✅', buildProgramId: buildProgram.id };
    }
    async findAll() {
        return this.prisma.buildProgram.findMany({
            include: {
                program: true,
                modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: {
                                        contenu: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
    }
    async remove(id) {
        return this.prisma.buildProgram.delete({
            where: { id },
        });
    }
    async update(id, body, file) {
        const { level, startDate, endDate, modules } = body;
        const parsedModules = typeof modules === 'string' ? JSON.parse(modules) : modules;
        await this.prisma.buildProgram.update({
            where: { id },
            data: {
                level: level || "Basique",
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                imageUrl: file ? `http://localhost:8000/uploads/sessions/${file.filename}` : undefined,
            },
        });
        await this.prisma.buildProgramContenu.deleteMany({
            where: { buildProgramCourse: { buildProgramModule: { buildProgramId: id } } },
        });
        await this.prisma.buildProgramCourse.deleteMany({
            where: { buildProgramModule: { buildProgramId: id } },
        });
        await this.prisma.buildProgramModule.deleteMany({
            where: { buildProgramId: id },
        });
        for (const mod of parsedModules) {
            const buildProgramModule = await this.prisma.buildProgramModule.create({
                data: {
                    buildProgramId: id,
                    moduleId: mod.moduleId,
                },
            });
            for (const course of mod.courses) {
                const buildProgramCourse = await this.prisma.buildProgramCourse.create({
                    data: {
                        buildProgramModuleId: buildProgramModule.id,
                        courseId: course.courseId,
                    },
                });
                for (const contenu of course.contenus) {
                    await this.prisma.buildProgramContenu.create({
                        data: {
                            buildProgramCourseId: buildProgramCourse.id,
                            contenuId: contenu.contenuId,
                        },
                    });
                }
            }
        }
        return { message: "✅ Program modifiée avec succès !" };
    }
    async findByProgramId(programId) {
        return this.prisma.buildProgram.findFirst({
            where: { programId },
            include: {
                program: true,
                modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: {
                                        contenu: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
    }
};
exports.buildProgramService = buildProgramService;
exports.buildProgramService = buildProgramService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], buildProgramService);
//# sourceMappingURL=buildProgram.service.js.map