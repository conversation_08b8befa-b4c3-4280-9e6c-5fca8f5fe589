"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatebuildProgramDto = exports.CreatebuildProgramModuleDto = exports.CreatebuildProgramCourseDto = exports.CreatebuildProgramContenuDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreatebuildProgramContenuDto {
    contenuId;
}
exports.CreatebuildProgramContenuDto = CreatebuildProgramContenuDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreatebuildProgramContenuDto.prototype, "contenuId", void 0);
class CreatebuildProgramCourseDto {
    courseId;
    contenus;
}
exports.CreatebuildProgramCourseDto = CreatebuildProgramCourseDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreatebuildProgramCourseDto.prototype, "courseId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreatebuildProgramContenuDto),
    __metadata("design:type", Array)
], CreatebuildProgramCourseDto.prototype, "contenus", void 0);
class CreatebuildProgramModuleDto {
    moduleId;
    courses;
}
exports.CreatebuildProgramModuleDto = CreatebuildProgramModuleDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreatebuildProgramModuleDto.prototype, "moduleId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreatebuildProgramCourseDto),
    __metadata("design:type", Array)
], CreatebuildProgramModuleDto.prototype, "courses", void 0);
class CreatebuildProgramDto {
    programId;
    startDate;
    endDate;
    modules;
}
exports.CreatebuildProgramDto = CreatebuildProgramDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreatebuildProgramDto.prototype, "programId", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreatebuildProgramDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreatebuildProgramDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreatebuildProgramModuleDto),
    __metadata("design:type", Array)
], CreatebuildProgramDto.prototype, "modules", void 0);
//# sourceMappingURL=create-buildProgram.dto.js.map