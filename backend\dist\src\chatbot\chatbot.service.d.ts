import { PrismaService } from 'nestjs-prisma';
export declare class ChatbotService {
    private prisma;
    private readonly logger;
    private ollamaUrl;
    private ollamaModel;
    constructor(prisma: PrismaService);
    private checkOllamaAvailability;
    processMessage(message: string): Promise<string>;
    private diagnoseOllama;
    private askOllama;
    executeQuery(query: string): Promise<any>;
}
