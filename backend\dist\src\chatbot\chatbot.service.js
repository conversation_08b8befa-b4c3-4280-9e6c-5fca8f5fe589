"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ChatbotService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatbotService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const axios_1 = require("axios");
const TABLES = [
    { prisma: 'user', keywords: ['user', 'users', 'utilisateur', 'utilisateurs', 'compte', 'email', 'profil'], label: 'name' },
    { prisma: 'program', keywords: ['program', 'programs', 'programme', 'formation', 'cours', 'programmes'], label: 'name' },
    { prisma: 'module', keywords: ['module', 'modules', 'chapitre', 'section'], label: 'name' },
    { prisma: 'course', keywords: ['course', 'courses', 'cours', 'leçon'], label: 'name' },
    { prisma: 'contenu', keywords: ['contenu', 'contenus', 'document', 'ressource', 'fichier'], label: 'title' },
    { prisma: 'quiz', keywords: ['quiz', 'quizzes', 'test', 'évaluation', 'question'], label: 'title' },
];
const DEFAULT_RESPONSES = {
    greeting: "Bonjour ! Je suis l'assistant de la plateforme LMS. Comment puis-je vous aider aujourd'hui ?",
    help: "Je peux vous aider avec des informations sur les utilisateurs, programmes, modules, cours, contenus et quiz. Vous pouvez me demander par exemple 'combien d'utilisateurs', 'liste des programmes', 'détail d'un cours', etc.",
    notUnderstood: "Je ne suis pas sûr de comprendre votre demande. Pourriez-vous reformuler ou me poser une question sur les utilisateurs, programmes, modules ou cours ?",
    error: "Je ne peux pas répondre à cette question pour le moment. Pourriez-vous me demander des informations sur la base de données, comme le nombre d'utilisateurs ou la liste des programmes ?"
};
function normalize(str) {
    return str
        .replace(/[''`]/g, "'")
        .replace(/\s+/g, ' ')
        .toLowerCase();
}
let ChatbotService = ChatbotService_1 = class ChatbotService {
    prisma;
    logger = new common_1.Logger(ChatbotService_1.name);
    ollamaUrl = process.env.OLLAMA_URL || 'http://localhost:11434';
    ollamaModel = process.env.OLLAMA_MODEL || 'llama3';
    constructor(prisma) {
        this.prisma = prisma;
        this.checkOllamaAvailability();
    }
    async checkOllamaAvailability() {
        try {
            const response = await axios_1.default.get(`${this.ollamaUrl}/api/tags`, { timeout: 5000 });
            this.logger.log(`Ollama disponible avec les modèles: ${JSON.stringify(response.data)}`);
            const models = response.data.models || [];
            const modelExists = models.some(model => model.name === this.ollamaModel);
            if (!modelExists) {
                this.logger.warn(`Le modèle ${this.ollamaModel} n'est pas disponible. Modèles disponibles: ${models.map(m => m.name).join(', ')}`);
            }
        }
        catch (error) {
            this.logger.error(`Ollama n'est pas disponible: ${error.message}`);
            if (error.code === 'ECONNREFUSED') {
                this.logger.error(`Assurez-vous qu'Ollama est en cours d'exécution sur ${this.ollamaUrl}`);
            }
        }
    }
    async processMessage(message) {
        try {
            const lowerMsg = normalize(message);
            if (lowerMsg.includes('pourquoi ollama') || lowerMsg.includes('ollama ne fonctionne pas')) {
                return await this.diagnoseOllama();
            }
            if (['hello', 'bonjour', 'salut', 'hi', 'hey'].some(greeting => lowerMsg.includes(greeting))) {
                return DEFAULT_RESPONSES.greeting;
            }
            if (['aide', 'help', 'aidez-moi', 'que peux-tu faire', 'what can you do'].some(help => lowerMsg.includes(help))) {
                return DEFAULT_RESPONSES.help;
            }
            if (lowerMsg.includes('select') && lowerMsg.includes('from')) {
                const results = await this.executeQuery(message);
                return `Résultats de la requête: ${JSON.stringify(results, null, 2)}`;
            }
            for (const { prisma, keywords } of TABLES) {
                for (const k of keywords) {
                    if (lowerMsg.match(new RegExp(`combien d['']? ?${k}\b`)) ||
                        lowerMsg.match(new RegExp(`combien de ${k}\b`)) ||
                        lowerMsg.match(new RegExp(`nombre de ${k}\b`))) {
                        const count = await this.prisma[prisma].count();
                        return `Il y a actuellement ${count} ${prisma}s dans la base de données.`;
                    }
                    if (lowerMsg.match(new RegExp(`how many[\w\s]*${k}\b`))) {
                        const count = await this.prisma[prisma].count();
                        return `There are currently ${count} ${prisma}s in this application.`;
                    }
                }
            }
            for (const { prisma, keywords } of TABLES) {
                if (keywords.some(k => lowerMsg.includes('liste des ' + k) || lowerMsg.includes('list of ' + k) || lowerMsg.includes('affiche les ' + k) || lowerMsg.includes('montre les ' + k))) {
                    const items = await this.prisma[prisma].findMany({ take: 10 });
                    if (items.length === 0)
                        return `Aucun(e) ${prisma} trouvé(e) dans la base de données.`;
                    return `Voici quelques ${prisma}s :\n` + items.map(i => JSON.stringify(i)).join('\n');
                }
            }
            for (const { prisma, keywords, label } of TABLES) {
                if (keywords.some(k => lowerMsg.includes('nom de ' + k) || lowerMsg.includes('noms de ' + k) || lowerMsg.includes('names of ' + k))) {
                    const items = await this.prisma[prisma].findMany({ take: 20, select: { [label]: true } });
                    if (items.length === 0)
                        return `Aucun(e) ${prisma} trouvé(e) dans la base de données.`;
                    return `Voici les noms des ${prisma}s :\n` + items.map(i => i[label]).filter(Boolean).join(', ');
                }
            }
            for (const { prisma, keywords } of TABLES) {
                if (keywords.some(k => lowerMsg.includes('détail') && lowerMsg.includes(k))) {
                    const items = await this.prisma[prisma].findMany({ take: 1 });
                    if (items.length === 0)
                        return `Aucun(e) ${prisma} trouvé(e) dans la base de données.`;
                    return `Détail d'un(e) ${prisma} :\n` + JSON.stringify(items[0], null, 2);
                }
            }
            try {
                const ollamaResponse = await this.askOllama(message);
                if (ollamaResponse && ollamaResponse.trim() !== '') {
                    return ollamaResponse;
                }
            }
            catch (error) {
                this.logger.error(`Erreur avec Ollama: ${error.message}`);
            }
            return DEFAULT_RESPONSES.notUnderstood;
        }
        catch (error) {
            this.logger.error(`Erreur lors du traitement du message: ${error.message}`);
            return "Je suis désolé, je n'ai pas pu traiter votre demande. Veuillez réessayer plus tard.";
        }
    }
    async diagnoseOllama() {
        try {
            const response = await axios_1.default.get(`${this.ollamaUrl}/api/tags`, { timeout: 5000 });
            const models = response.data.models || [];
            const modelsList = models.map(m => m.name).join(', ');
            if (models.length === 0) {
                return `Ollama est en cours d'exécution sur ${this.ollamaUrl}, mais aucun modèle n'est disponible. Exécutez 'ollama pull ${this.ollamaModel}' pour télécharger un modèle.`;
            }
            const modelExists = models.some(model => model.name === this.ollamaModel);
            if (!modelExists) {
                return `Ollama est en cours d'exécution sur ${this.ollamaUrl}, mais le modèle '${this.ollamaModel}' n'est pas disponible. Modèles disponibles: ${modelsList}. Exécutez 'ollama pull ${this.ollamaModel}' pour télécharger ce modèle.`;
            }
            try {
                const testResponse = await axios_1.default.post(`${this.ollamaUrl}/api/generate`, {
                    model: this.ollamaModel,
                    prompt: "Réponds simplement par 'OK' pour tester la connexion.",
                    stream: false
                }, { timeout: 10000 });
                if (testResponse.data?.response) {
                    return `Ollama fonctionne correctement sur ${this.ollamaUrl} avec le modèle '${this.ollamaModel}'. Modèles disponibles: ${modelsList}. Le problème pourrait être lié à la façon dont l'application communique avec Ollama.`;
                }
                else {
                    return `Ollama est accessible sur ${this.ollamaUrl}, mais la génération de texte a échoué. Format de réponse inattendu: ${JSON.stringify(testResponse.data)}`;
                }
            }
            catch (genError) {
                return `Ollama est accessible sur ${this.ollamaUrl}, mais la génération de texte a échoué: ${genError.message}. Vérifiez que le modèle '${this.ollamaModel}' fonctionne correctement.`;
            }
        }
        catch (error) {
            if (error.code === 'ECONNREFUSED') {
                return `Ollama n'est pas accessible sur ${this.ollamaUrl}. Assurez-vous qu'Ollama est installé et en cours d'exécution. Erreur: Connexion refusée.`;
            }
            else if (error.code === 'ETIMEDOUT') {
                return `Ollama n'a pas répondu à temps sur ${this.ollamaUrl}. Vérifiez qu'Ollama est en cours d'exécution et que l'URL est correcte.`;
            }
            else {
                return `Ollama n'est pas accessible: ${error.message}. Assurez-vous qu'Ollama est installé et en cours d'exécution sur ${this.ollamaUrl}.`;
            }
        }
    }
    async askOllama(message) {
        try {
            this.logger.log(`Envoi de la requête à Ollama: ${message}`);
            const response = await axios_1.default.post(`${this.ollamaUrl}/api/generate`, {
                model: this.ollamaModel,
                prompt: `Tu es un assistant pour une plateforme LMS (Learning Management System). 
                Réponds de manière concise et utile à la question suivante: ${message}`,
                stream: false
            }, {
                timeout: 15000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            this.logger.log(`Réponse d'Ollama reçue`);
            if (response.data?.response) {
                return response.data.response;
            }
            else {
                this.logger.error(`Format de réponse Ollama inattendu: ${JSON.stringify(response.data)}`);
                return null;
            }
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'appel à Ollama: ${error.message}`);
            if (error.response) {
                this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
            }
            throw new Error(`Service Ollama non disponible: ${error.message}`);
        }
    }
    async executeQuery(query) {
        try {
            if (!query.toLowerCase().trim().startsWith('select')) {
                throw new Error('Seules les requêtes SELECT sont autorisées');
            }
            const results = await this.prisma.$queryRawUnsafe(query);
            return results;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'exécution de la requête SQL: ${error.message}`);
            return { error: error.message };
        }
    }
};
exports.ChatbotService = ChatbotService;
exports.ChatbotService = ChatbotService = ChatbotService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], ChatbotService);
//# sourceMappingURL=chatbot.service.js.map