import { ContenusService } from './contenu.service';
import { PrismaService } from 'nestjs-prisma';
export declare class ContenusController {
    private readonly contenusService;
    private readonly prisma;
    constructor(contenusService: ContenusService, prisma: PrismaService);
    uploadFile(file: Express.Multer.File, body: any): Promise<{
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    }>;
    findAll(): import(".prisma/client").Prisma.PrismaPromise<({
        courseContenus: {
            id: number;
            courseId: number;
            contenuId: number;
        }[];
    } & {
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    })[]>;
    remove(id: string): import(".prisma/client").Prisma.Prisma__ContenuClient<{
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
}
