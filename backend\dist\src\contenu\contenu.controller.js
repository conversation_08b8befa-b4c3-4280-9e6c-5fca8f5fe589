"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContenusController = void 0;
const common_1 = require("@nestjs/common");
const contenu_service_1 = require("./contenu.service");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const nestjs_prisma_1 = require("nestjs-prisma");
const storage = (0, multer_1.diskStorage)({
    destination: './uploads',
    filename: (req, file, cb) => {
        const unique = `${Date.now()}${(0, path_1.extname)(file.originalname)}`;
        cb(null, unique);
    },
});
let ContenusController = class ContenusController {
    contenusService;
    prisma;
    constructor(contenusService, prisma) {
        this.contenusService = contenusService;
        this.prisma = prisma;
    }
    async uploadFile(file, body) {
        const { title, type, fileType, courseIds } = body;
        if (!file && (type === 'Cours' || type === 'Exercice')) {
            throw new Error('Un fichier est requis pour les types de contenu Cours et Exercice');
        }
        const contentData = {
            title,
            type,
            fileType: file ? fileType : 'PDF',
            fileUrl: file
                ? `http://localhost:8000/uploads/${file.filename}`
                : 'placeholder.pdf',
            courseContenus: {
                create: courseIds && courseIds !== 'undefined' && courseIds !== 'null'
                    ? (() => {
                        try {
                            const parsedIds = JSON.parse(courseIds);
                            console.log('Parsed courseIds:', parsedIds);
                            return parsedIds.map((courseId) => ({
                                course: { connect: { id: typeof courseId === 'string' ? parseInt(courseId) : courseId } },
                            }));
                        }
                        catch (error) {
                            console.error('Error parsing courseIds:', error);
                            return [];
                        }
                    })()
                    : [],
            },
        };
        console.log('Creating content with data:', contentData);
        const newContenu = await this.prisma.contenu.create({
            data: contentData,
        });
        return newContenu;
    }
    findAll() {
        return this.contenusService.findAll();
    }
    remove(id) {
        return this.contenusService.remove(+id);
    }
};
exports.ContenusController = ContenusController;
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', { storage })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ContenusController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ContenusController.prototype, "findAll", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ContenusController.prototype, "remove", null);
exports.ContenusController = ContenusController = __decorate([
    (0, common_1.Controller)('contenus'),
    __metadata("design:paramtypes", [contenu_service_1.ContenusService,
        nestjs_prisma_1.PrismaService])
], ContenusController);
//# sourceMappingURL=contenu.controller.js.map