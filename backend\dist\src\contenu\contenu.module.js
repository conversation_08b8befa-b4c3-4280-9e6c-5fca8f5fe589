"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContenuModule = void 0;
const common_1 = require("@nestjs/common");
const contenu_service_1 = require("./contenu.service");
const contenu_controller_1 = require("./contenu.controller");
const nestjs_prisma_1 = require("nestjs-prisma");
let ContenuModule = class ContenuModule {
};
exports.ContenuModule = ContenuModule;
exports.ContenuModule = ContenuModule = __decorate([
    (0, common_1.Module)({
        controllers: [contenu_controller_1.ContenusController],
        providers: [contenu_service_1.ContenusService, nestjs_prisma_1.PrismaService],
    })
], ContenuModule);
//# sourceMappingURL=contenu.module.js.map