import { PrismaService } from 'nestjs-prisma';
import { CreateContenuDto } from './dto/create-contenu.dto';
export declare class ContenusService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: CreateContenuDto): Promise<{
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        published: boolean;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    }>;
    findAll(): import(".prisma/client").Prisma.PrismaPromise<({
        courseContenus: {
            id: number;
            courseId: number;
            contenuId: number;
        }[];
    } & {
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        published: boolean;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    })[]>;
    remove(id: number): import(".prisma/client").Prisma.Prisma__ContenuClient<{
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        published: boolean;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
    updatePublishStatus(id: number, published: boolean): Promise<{
        id: number;
        type: import(".prisma/client").$Enums.ContenuType;
        title: string;
        published: boolean;
        fileUrl: string | null;
        fileType: import(".prisma/client").$Enums.FileType | null;
    }>;
    publishContenu(id: number): Promise<{
        message: string;
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            published: boolean;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
    }>;
}
