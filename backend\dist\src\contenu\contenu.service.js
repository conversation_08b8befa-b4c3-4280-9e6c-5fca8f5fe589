"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContenusService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const common_2 = require("@nestjs/common");
let ContenusService = class ContenusService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        const { courseIds, ...contenuData } = data;
        console.log('Creating content with data:', contenuData);
        const created = await this.prisma.contenu.create({
            data: contenuData,
        });
        if (courseIds?.length) {
            await this.prisma.courseContenu.createMany({
                data: courseIds.map((courseId) => ({
                    courseId,
                    contenuId: created.id,
                })),
            });
        }
        return created;
    }
    findAll() {
        return this.prisma.contenu.findMany({
            include: { courseContenus: true },
        });
    }
    remove(id) {
        return this.prisma.contenu.delete({ where: { id } });
    }
    async updatePublishStatus(id, published) {
        return this.prisma.contenu.update({
            where: { id },
            data: { published },
        });
    }
    async publishContenu(id) {
        const contenu = await this.prisma.contenu.findUnique({
            where: { id },
        });
        if (!contenu) {
            throw new common_2.NotFoundException('Contenu non trouvé');
        }
        const updated = await this.prisma.contenu.update({
            where: { id },
            data: { published: !contenu.published },
        });
        return {
            message: `Contenu ${updated.published ? 'publié' : 'dépublié'} avec succès ✅`,
            contenu: updated,
        };
    }
};
exports.ContenusService = ContenusService;
exports.ContenusService = ContenusService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], ContenusService);
//# sourceMappingURL=contenu.service.js.map