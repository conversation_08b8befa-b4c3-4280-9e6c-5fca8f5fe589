import { CreateCourseDto } from './dto/create-course.dto';
import { PrismaService } from 'nestjs-prisma';
export declare class CoursesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: CreateCourseDto): import(".prisma/client").Prisma.Prisma__CourseClient<{
        id: number;
        title: string;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
    findAll(): import(".prisma/client").Prisma.PrismaPromise<{
        id: number;
        title: string;
    }[]>;
    remove(id: number): import(".prisma/client").Prisma.Prisma__CourseClient<{
        id: number;
        title: string;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
}
