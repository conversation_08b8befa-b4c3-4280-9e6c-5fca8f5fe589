import { FeedbackService } from './feedback.service';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { CreateFeedbackResponseDto } from './dto/create-feedback-response.dto';
import { QueryFeedbackDto } from './dto/query-feedback.dto';
import { UpdateFeedbackDto } from './dto/update-feedback.dto';
export declare class FeedbackController {
    private readonly feedbackService;
    constructor(feedbackService: FeedbackService);
    create(createFeedbackDto: CreateFeedbackDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
    findAll(query: QueryFeedbackDto): Promise<({
        responses: {
            id: number;
            createdAt: Date;
            response: string;
            responderId: number | null;
            feedbackId: number;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    })[]>;
    getStats(): Promise<{
        totalFeedbacks: number;
        averageRating: number;
        categoryBreakdown: {
            category: string;
            count: unknown;
            percentage: number;
        }[];
        recentFeedbackCount: number;
        pendingResponses: number;
    }>;
    getAnalytics(timeRange: string): Promise<{
        ratingData: {
            name: string;
            count: any;
        }[];
        categoryData: {
            name: string;
            value: unknown;
        }[];
        timelineData: {
            day: string;
            count: unknown;
        }[] | {
            month: string;
            count: unknown;
        }[];
    }>;
    findOne(id: string): Promise<{
        responses: {
            id: number;
            createdAt: Date;
            response: string;
            responderId: number | null;
            feedbackId: number;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
    update(id: string, updateFeedbackDto: UpdateFeedbackDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
    respond(id: string, createResponseDto: CreateFeedbackResponseDto): Promise<{
        id: number;
        createdAt: Date;
        response: string;
        responderId: number | null;
        feedbackId: number;
    }>;
    like(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
    dislike(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
    report(id: string): Promise<{
        responses: {
            id: number;
            createdAt: Date;
            response: string;
            responderId: number | null;
            feedbackId: number;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
    remove(id: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        senderId: number | null;
        receiverId: number | null;
        likes: number;
        dislikes: number;
    }>;
}
