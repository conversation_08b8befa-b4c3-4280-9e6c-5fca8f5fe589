"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let FeedbackService = class FeedbackService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createFeedbackDto) {
        return this.prisma.feedback.create({
            data: createFeedbackDto,
        });
    }
    async findAll(filters = {}) {
        const { search, ...restFilters } = filters;
        const where = { ...restFilters };
        if (search) {
            where.OR = [
                { message: { contains: search, mode: 'insensitive' } },
                { category: { contains: search, mode: 'insensitive' } },
                { type: { contains: search, mode: 'insensitive' } },
            ];
        }
        return this.prisma.feedback.findMany({
            where,
            include: {
                responses: true,
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findOne(id) {
        const feedback = await this.prisma.feedback.findUnique({
            where: { id },
            include: {
                responses: true,
            },
        });
        if (!feedback) {
            throw new common_1.NotFoundException(`Feedback with ID ${id} not found`);
        }
        return feedback;
    }
    async update(id, updateFeedbackDto) {
        await this.findOne(id);
        return this.prisma.feedback.update({
            where: { id },
            data: {
                ...updateFeedbackDto,
                updatedAt: new Date(),
            },
        });
    }
    async remove(id) {
        await this.findOne(id);
        return this.prisma.feedback.delete({
            where: { id },
        });
    }
    async createResponse(id, dto) {
        await this.findOne(id);
        return this.prisma.feedbackResponse.create({
            data: {
                ...dto,
                feedbackId: id,
            },
        });
    }
    async like(id) {
        const feedback = await this.findOne(id);
        return this.prisma.feedback.update({
            where: { id },
            data: { likes: feedback.likes + 1 },
        });
    }
    async dislike(id) {
        const feedback = await this.findOne(id);
        return this.prisma.feedback.update({
            where: { id },
            data: { dislikes: feedback.dislikes + 1 },
        });
    }
    async getStats() {
        const feedbacks = await this.prisma.feedback.findMany({
            include: {
                responses: true
            }
        });
        const totalFeedbacks = feedbacks.length;
        const totalRating = feedbacks.reduce((sum, fb) => sum + (fb.rating || 0), 0);
        const averageRating = totalFeedbacks > 0 ? totalRating / totalFeedbacks : 0;
        const categories = {};
        feedbacks.forEach(fb => {
            if (fb.category) {
                categories[fb.category] = (categories[fb.category] || 0) + 1;
            }
        });
        const categoryBreakdown = Object.entries(categories).map(([category, count]) => ({
            category,
            count,
            percentage: Math.round(count / totalFeedbacks * 100),
        }));
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const recentFeedbackCount = feedbacks.filter(fb => new Date(fb.createdAt) >= oneWeekAgo).length;
        const pendingResponses = feedbacks.filter(fb => !fb.responses || fb.responses.length === 0).length;
        return {
            totalFeedbacks,
            averageRating,
            categoryBreakdown,
            recentFeedbackCount,
            pendingResponses,
        };
    }
    async getAnalytics(timeRange = '6months') {
        const feedbacks = await this.prisma.feedback.findMany({
            include: {
                responses: true
            }
        });
        const filteredFeedbacks = this.filterByTimeRange(feedbacks, timeRange);
        const ratingData = [5, 4, 3, 2, 1].map(rating => ({
            name: `${rating} Stars`,
            count: filteredFeedbacks.filter(fb => Math.round(fb.rating) === rating).length,
        }));
        const categories = {};
        filteredFeedbacks.forEach(fb => {
            if (fb.category) {
                categories[fb.category] = (categories[fb.category] || 0) + 1;
            }
        });
        const categoryData = Object.entries(categories).map(([name, value]) => ({
            name,
            value,
        }));
        const timelineData = this.generateTimelineData(filteredFeedbacks, timeRange);
        return {
            ratingData,
            categoryData,
            timelineData,
        };
    }
    filterByTimeRange(feedbacks, timeRange) {
        const now = new Date();
        let cutoffDate;
        switch (timeRange) {
            case '30days':
                cutoffDate = new Date(now.setDate(now.getDate() - 30));
                break;
            case '3months':
                cutoffDate = new Date(now.setMonth(now.getMonth() - 3));
                break;
            case '6months':
                cutoffDate = new Date(now.setMonth(now.getMonth() - 6));
                break;
            case '1year':
                cutoffDate = new Date(now.setFullYear(now.getFullYear() - 1));
                break;
            case 'all':
            default:
                return feedbacks;
        }
        return feedbacks.filter(fb => new Date(fb.createdAt) >= cutoffDate);
    }
    generateTimelineData(feedbacks, timeRange) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        if (timeRange === '30days') {
            const dailyData = {};
            const now = new Date();
            for (let i = 0; i < 30; i++) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                const dateStr = `${date.getDate()} ${months[date.getMonth()]}`;
                dailyData[dateStr] = 0;
            }
            feedbacks.forEach(fb => {
                const date = new Date(fb.createdAt);
                const dateStr = `${date.getDate()} ${months[date.getMonth()]}`;
                if (dailyData[dateStr] !== undefined) {
                    dailyData[dateStr]++;
                }
            });
            return Object.entries(dailyData).reverse().map(([day, count]) => ({
                day,
                count,
            }));
        }
        else {
            const monthlyData = {};
            months.forEach(month => {
                monthlyData[month] = 0;
            });
            feedbacks.forEach(fb => {
                const month = months[new Date(fb.createdAt).getMonth()];
                monthlyData[month]++;
            });
            return Object.entries(monthlyData).map(([month, count]) => ({
                month,
                count,
            }));
        }
    }
};
exports.FeedbackService = FeedbackService;
exports.FeedbackService = FeedbackService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], FeedbackService);
//# sourceMappingURL=feedback.service.js.map