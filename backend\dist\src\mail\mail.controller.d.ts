import { MailService } from './mail.service';
import { PrismaService } from 'nestjs-prisma';
export declare class MailController {
    private readonly mailService;
    private readonly prisma;
    constructor(mailService: MailService, prisma: PrismaService);
    forgotPassword(email: string): Promise<{
        message: string;
    }>;
    testPasswordChangeEmail(body: {
        email: string;
        ipAddress?: string;
    }): Promise<{
        success: boolean;
        message: string;
        details: {
            email: string;
            timestamp: string;
            ipAddress: string;
        };
    }>;
    testPasswordResetV2(body: {
        email: string;
    }): Promise<{
        success: boolean;
        message: string;
        details: {
            email: string;
            token: string;
            template: string;
        };
    }>;
}
