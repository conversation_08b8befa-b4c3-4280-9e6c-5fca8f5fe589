"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailController = void 0;
const common_1 = require("@nestjs/common");
const mail_service_1 = require("./mail.service");
const nestjs_prisma_1 = require("nestjs-prisma");
const crypto = require("crypto");
let MailController = class MailController {
    mailService;
    prisma;
    constructor(mailService, prisma) {
        this.mailService = mailService;
        this.prisma = prisma;
    }
    async forgotPassword(email) {
        const user = await this.prisma.user.findUnique({ where: { email } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const token = crypto.randomBytes(32).toString('hex');
        await this.prisma.user.update({
            where: { email },
            data: {
                resetToken: token,
                resetTokenExpiry: new Date(Date.now() + 1000 * 60 * 120),
            },
        });
        await this.mailService.sendPasswordResetEmail(email, token);
        return { message: 'Reset email sent' };
    }
    async testPasswordChangeEmail(body) {
        try {
            const timestamp = new Date().toLocaleString('fr-FR', {
                timeZone: 'Europe/Paris',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            await this.mailService.sendPasswordChangeConfirmationEmail(body.email, timestamp, body.ipAddress || '*************');
            return {
                success: true,
                message: 'Password change confirmation email sent successfully',
                details: {
                    email: body.email,
                    timestamp,
                    ipAddress: body.ipAddress || '*************'
                }
            };
        }
        catch (error) {
            throw new common_1.NotFoundException(`Failed to send email: ${error.message}`);
        }
    }
    async testPasswordResetV2(body) {
        try {
            const token = crypto.randomBytes(32).toString('hex');
            await this.mailService.sendPasswordResetEmailV2(body.email, token);
            return {
                success: true,
                message: 'New password reset email (V2) sent successfully',
                details: {
                    email: body.email,
                    token: token.substring(0, 8) + '...',
                    template: 'Version 2 - Design moderne avec gradient'
                }
            };
        }
        catch (error) {
            throw new common_1.NotFoundException(`Failed to send email: ${error.message}`);
        }
    }
};
exports.MailController = MailController;
__decorate([
    (0, common_1.Post)('forgot-password'),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MailController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('test-password-change-email'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MailController.prototype, "testPasswordChangeEmail", null);
__decorate([
    (0, common_1.Post)('test-password-reset-v2'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MailController.prototype, "testPasswordResetV2", null);
exports.MailController = MailController = __decorate([
    (0, common_1.Controller)('mail'),
    __metadata("design:paramtypes", [mail_service_1.MailService,
        nestjs_prisma_1.PrismaService])
], MailController);
//# sourceMappingURL=mail.controller.js.map