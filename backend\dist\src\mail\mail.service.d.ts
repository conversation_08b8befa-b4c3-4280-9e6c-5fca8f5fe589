import { MailerService } from '@nestjs-modules/mailer';
export declare class MailService {
    private readonly mailerService;
    constructor(mailerService: MailerService);
    private send;
    sendPasswordResetEmail(to: string, token: string): Promise<SentMessageInfo>;
    sendWelcomeEmail(to: string, tempPassword: string, role: string): Promise<SentMessageInfo>;
    sendPasswordChangeConfirmationEmail(to: string, timestamp: string, ipAddress?: string): Promise<SentMessageInfo>;
}
