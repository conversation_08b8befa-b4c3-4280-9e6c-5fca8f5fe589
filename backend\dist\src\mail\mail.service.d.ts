import { MailerService } from '@nestjs-modules/mailer';
export declare class MailService {
    private readonly mailerService;
    constructor(mailerService: MailerService);
    private send;
    sendPasswordResetEmail(to: string, token: string): Promise<any>;
    sendWelcomeEmail(to: string, tempPassword: string, role: string): Promise<any>;
    sendPasswordChangeConfirmationEmail(to: string, timestamp: string, ipAddress?: string): Promise<any>;
    sendPasswordResetEmailV2(to: string, token: string): Promise<any>;
    sendEmailVerificationCode(to: string, code: string): Promise<void>;
    sendWelcomeEmailverification(to: string, tempPassword: string, role: string): Promise<void>;
}
