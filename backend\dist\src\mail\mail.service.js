"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailService = void 0;
const common_1 = require("@nestjs/common");
const mailer_1 = require("@nestjs-modules/mailer");
let MailService = class MailService {
    mailerService;
    constructor(mailerService) {
        this.mailerService = mailerService;
    }
    async send(to, subject, html) {
        try {
            const response = await this.mailerService.sendMail({
                from: 'LMS Platform <<EMAIL>>',
                to,
                subject,
                html,
            });
            console.log("Email sent successfully:", response);
            return response;
        }
        catch (error) {
            console.log("Failed to send email:", error);
        }
    }
    async sendPasswordResetEmail(to, token) {
        const resetLink = `http://localhost:3000/ResetPasswordPage?token=${token}&email=${to}`;
        return this.send(to, 'Réinitialisation de mot de passe', `
      <div style="font-family:Arial;max-width:600px;margin:0 auto;padding:20px;border:1px solid #e0e0e0;border-radius:5px">
        <h2 style="color:#1976d2;text-align:center">Réinitialisation de mot de passe</h2>
        <p>Code de réinitialisation :</p>
        <div style="background:#f5f5f5;padding:15px;border-radius:5px;margin:20px 0;text-align:center">
          <p style="font-family:monospace;font-size:24px;font-weight:bold;letter-spacing:2px">${token}</p>
        </div>
        <div style="text-align:center;margin-top:30px">
          <a href="${resetLink}" style="background:#1976d2;color:white;padding:10px 20px;text-decoration:none;border-radius:4px">Réinitialiser</a>
        </div>
      </div>
    `);
    }
    async sendWelcomeEmail(to, tempPassword, role) {
        return this.send(to, '🎓 Bienvenue sur la plateforme LMS', `
      <div style="font-family:Arial;max-width:600px;margin:0 auto;padding:20px;border:1px solid #e0e0e0;border-radius:5px">
        <h2 style="color:#1976d2;text-align:center">Bienvenue sur la plateforme LMS!</h2>
        <p>Votre compte a été créé avec succès. Voici vos identifiants de connexion :</p>
        <div style="background:#f5f5f5;padding:15px;border-radius:5px;margin:20px 0">
          <p><strong>Email:</strong> ${to}</p>
          <p><strong>Mot de passe temporaire:</strong> <span style="font-family:monospace;background:#e0e0e0;padding:3px 6px;border-radius:3px">${tempPassword}</span></p>
          <p><strong>Rôle:</strong> ${role}</p>
        </div>
        <p style="color:#666;font-size:14px;margin:15px 0">
          Cliquez sur le bouton ci-dessous pour vous connecter à la plateforme avec vos identifiants :
        </p>
        <div style="text-align:center;margin-top:30px">
          <a href="http://localhost:3000/login?forceLogout=true&email=${encodeURIComponent(to)}"
             style="background:#1976d2;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;font-weight:bold"
             target="_blank">
            Se connecter
          </a>
        </div>


      </div>
    `);
    }
    async sendPasswordChangeConfirmationEmail(to, timestamp, ipAddress) {
        const loginLink = `http://localhost:3000/login`;
        return this.send(to, '🔒 Confirmation de changement de mot de passe', `
      <div style="font-family:Arial;max-width:600px;margin:0 auto;padding:20px;border:1px solid #e0e0e0;border-radius:5px">
        <h2 style="color:#1976d2;text-align:center">Mot de passe modifié avec succès</h2>

        <div style="background:#e8f5e8;border:1px solid #4caf50;border-radius:5px;padding:15px;margin:20px 0">
          <p style="color:#2e7d32;margin:0;font-weight:bold">✅ Votre mot de passe a été modifié avec succès</p>
        </div>

        <p>Votre mot de passe a été réinitialisé et modifié avec succès sur la plateforme LMS.</p>

        <div style="background:#f5f5f5;padding:15px;border-radius:5px;margin:20px 0">
          <h3 style="color:#333;margin-top:0">Détails de la modification :</h3>
          <p><strong>Date et heure :</strong> ${timestamp}</p>
          ${ipAddress ? `<p><strong>Adresse IP :</strong> ${ipAddress}</p>` : ''}
          <p><strong>Action :</strong> Réinitialisation de mot de passe</p>
        </div>

        <div style="background:#fff3cd;border:1px solid #ffc107;border-radius:5px;padding:15px;margin:20px 0">
          <h3 style="color:#856404;margin-top:0">⚠️ Important - Sécurité</h3>
          <p style="color:#856404;margin:0">
            Si vous n'avez pas effectué cette modification, votre compte pourrait être compromis.
            Veuillez immédiatement :
          </p>
          <ul style="color:#856404;margin:10px 0">
            <li>Vous connecter et changer votre mot de passe</li>
            <li>Vérifier vos informations de compte</li>
            <li>Contacter notre support technique</li>
          </ul>
        </div>

        <div style="text-align:center;margin-top:30px">
          <a href="${loginLink}"
             style="background:#1976d2;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;font-weight:bold"
             target="_blank">
            Se connecter à la plateforme
          </a>
        </div>

        <div style="margin-top:30px;padding-top:20px;border-top:1px solid #e0e0e0;color:#666;font-size:12px">
          <p>Si vous avez des questions ou des préoccupations concernant la sécurité de votre compte,
          contactez notre équipe de support à <strong><EMAIL></strong></p>
          <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
        </div>
      </div>
    `);
    }
};
exports.MailService = MailService;
exports.MailService = MailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [mailer_1.MailerService])
], MailService);
//# sourceMappingURL=mail.service.js.map