"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixelController = void 0;
const common_1 = require("@nestjs/common");
let PixelController = class PixelController {
    handlePixelOpen(email, res) {
        console.log(`📬 Tracking pixel triggered by: ${email}`);
        const img = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVQI12NgYAAAAAMAASsJTYQAAAAASUVORK5CYII=', 'base64');
        res.setHeader('Content-Type', 'image/png');
        res.setHeader('Content-Length', img.length.toString());
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.end(img);
    }
};
exports.PixelController = PixelController;
__decorate([
    (0, common_1.Get)('open'),
    __param(0, (0, common_1.Query)('email')),
    __param(1, (0, common_1.Res)({ passthrough: false })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PixelController.prototype, "handlePixelOpen", null);
exports.PixelController = PixelController = __decorate([
    (0, common_1.Controller)('track')
], PixelController);
//# sourceMappingURL=pixel.controller.js.map