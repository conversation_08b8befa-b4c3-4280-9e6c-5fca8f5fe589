import { PrismaService } from 'nestjs-prisma';
import { CreateModuleDto } from './dto/create-module.dto';
export declare class ModulesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: CreateModuleDto): import(".prisma/client").Prisma.Prisma__ModuleClient<{
        id: number;
        name: string;
        periodUnit: import(".prisma/client").$Enums.PeriodUnit;
        duration: number;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
    findAll(): import(".prisma/client").Prisma.PrismaPromise<{
        id: number;
        name: string;
        periodUnit: import(".prisma/client").$Enums.PeriodUnit;
        duration: number;
    }[]>;
    remove(id: number): import(".prisma/client").Prisma.Prisma__ModuleClient<{
        id: number;
        name: string;
        periodUnit: import(".prisma/client").$Enums.PeriodUnit;
        duration: number;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
}
