import { ProgramsService } from './programs.service';
import { CreateProgramDto } from './dto/create-program.dto';
export declare class ProgramsController {
    private readonly programsService;
    constructor(programsService: ProgramsService);
    create(dto: CreateProgramDto): Promise<{
        id: number;
        name: string;
    }>;
    findAll(): Promise<{
        id: number;
        name: string;
    }[]>;
    remove(id: string): Promise<{
        id: number;
        name: string;
    }>;
}
