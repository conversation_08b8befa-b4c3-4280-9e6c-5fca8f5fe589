import { ProgramsService } from './programs.service';
import { CreateProgramDto } from './dto/create-program.dto';
export declare class ProgramsController {
    private readonly programsService;
    constructor(programsService: ProgramsService);
    create(dto: CreateProgramDto): Promise<{
        id: number;
        name: string;
        published: boolean;
    }>;
    findAll(): Promise<{
        id: number;
        name: string;
        published: boolean;
    }[]>;
    remove(id: string): Promise<{
        id: number;
        name: string;
        published: boolean;
    }>;
    rebuildProgram(id: string, body: any): Promise<{
        message: string;
    }>;
    update(id: string, body: any): Promise<{
        id: number;
        name: string;
        published: boolean;
    }>;
    publishProgram(id: number): Promise<{
        message: string;
        program: {
            id: number;
            name: string;
            published: boolean;
        };
    }>;
}
