import { PrismaService } from 'nestjs-prisma';
import { CreateProgramDto } from './dto/create-program.dto';
export declare class ProgramsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: CreateProgramDto): Promise<{
        id: number;
        name: string;
    }>;
    findAll(): Promise<{
        id: number;
        name: string;
    }[]>;
    remove(id: number): Promise<{
        id: number;
        name: string;
    }>;
}
