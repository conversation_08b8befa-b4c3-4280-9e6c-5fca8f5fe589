import { PrismaService } from 'nestjs-prisma';
import { CreateProgramDto } from './dto/create-program.dto';
export declare class ProgramsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(data: CreateProgramDto): Promise<{
        id: number;
        name: string;
        published: boolean;
    }>;
    findAll(): Promise<{
        id: number;
        name: string;
        published: boolean;
    }[]>;
    remove(id: number): Promise<{
        id: number;
        name: string;
        published: boolean;
    }>;
    rebuildProgram(id: number, data: any): Promise<{
        message: string;
    }>;
    update(id: number, data: any): Promise<{
        id: number;
        name: string;
        published: boolean;
    }>;
    publishProgram(id: number): Promise<{
        message: string;
        program: {
            id: number;
            name: string;
            published: boolean;
        };
    }>;
}
