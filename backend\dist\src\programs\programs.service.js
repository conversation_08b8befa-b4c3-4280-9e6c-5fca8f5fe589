"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProgramsService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let ProgramsService = class ProgramsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        return this.prisma.program.create({ data });
    }
    async findAll() {
        return this.prisma.program.findMany();
    }
    async remove(id) {
        return this.prisma.program.delete({ where: { id } });
    }
    async rebuildProgram(id, data) {
        const { name, level, modules } = data;
        await this.prisma.program.update({
            where: { id },
            data: { name },
        });
        await this.prisma.programModule.deleteMany({ where: { programId: id } });
        for (const mod of modules) {
            const { moduleId, courses } = mod;
            await this.prisma.programModule.create({
                data: {
                    programId: id,
                    moduleId,
                },
            });
            for (const course of courses) {
                const { courseId, contenus } = course;
                await this.prisma.moduleCourse.create({
                    data: {
                        moduleId,
                        courseId,
                    },
                });
                for (const contenu of contenus) {
                    await this.prisma.courseContenu.create({
                        data: {
                            courseId,
                            contenuId: contenu.contenuId,
                        },
                    });
                }
            }
        }
        return { message: 'Programme reconstruit avec succès ✅' };
    }
    async update(id, data) {
        return this.prisma.program.update({
            where: { id },
            data,
        });
    }
    async publishProgram(id) {
        const program = await this.prisma.program.findUnique({
            where: { id },
        });
        if (!program) {
            throw new common_1.NotFoundException('Programme non trouvé');
        }
        const updated = await this.prisma.program.update({
            where: { id },
            data: { published: !program.published },
        });
        return {
            message: `Programme ${updated.published ? 'publié' : 'dépublié'} avec succès ✅`,
            program: updated,
        };
    }
};
exports.ProgramsService = ProgramsService;
exports.ProgramsService = ProgramsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], ProgramsService);
//# sourceMappingURL=programs.service.js.map