export declare class CreateChoiceDto {
    text?: string;
    imageUrl?: string;
    isCorrect: number;
}
export declare class CreateQuestionDto {
    text: string;
    imageUrl?: string;
    type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'IMAGE_CHOICE';
    score: number;
    negativeMark?: number;
    correctText?: string;
    choices: CreateChoiceDto[];
}
export declare class CreateQuizDto {
    contenuId: number;
    title?: string;
    description?: string;
    timeLimit?: number;
    questions: CreateQuestionDto[];
}
