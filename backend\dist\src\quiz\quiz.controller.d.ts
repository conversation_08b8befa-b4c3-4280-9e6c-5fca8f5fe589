import { QuizService } from './quiz.service';
import { CreateQuizDto } from './dto/create-quiz.dto';
export declare class QuizController {
    private readonly quizService;
    constructor(quizService: QuizService);
    create(createQuizDto: CreateQuizDto): Promise<{
        message: string;
        quizId: number;
    }>;
    getQuizByContenu(contenuId: string): Promise<{
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    }>;
    findOne(id: string): Promise<({
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            published: boolean;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    }) | null>;
    updateByContenuId(contenuId: string, data: {
        timeLimit: number;
        questions: any[];
    }): Promise<{
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    }>;
    uploadQuestionImage(file: Express.Multer.File): {
        imageUrl: string;
    };
}
