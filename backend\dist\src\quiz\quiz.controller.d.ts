import { QuizService } from './quiz.service';
import { CreateQuizDto } from './dto/create-quiz.dto';
export declare class QuizController {
    private readonly quizService;
    constructor(quizService: QuizService);
    create(createQuizDto: CreateQuizDto): Promise<{
        message: string;
        quizId: number;
    }>;
    getQuizByContenu(contenuId: string): Promise<({
        choices: {
            id: number;
            text: string;
            isCorrect: boolean;
            questionId: number;
        }[];
    } & {
        id: number;
        text: string;
        quizId: number;
    })[]>;
    findOne(id: string): Promise<({
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
        questions: ({
            choices: {
                id: number;
                text: string;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            text: string;
            quizId: number;
        })[];
    } & {
        id: number;
        contenuId: number;
    }) | null>;
}
