import { CreateQuizDto } from './dto/create-quiz.dto';
import { PrismaService } from 'nestjs-prisma';
export declare class QuizService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createQuizDto: CreateQuizDto): Promise<{
        message: string;
        quizId: number;
    }>;
    findAll(): Promise<({
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
        questions: ({
            choices: {
                id: number;
                text: string;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            text: string;
            quizId: number;
        })[];
    } & {
        id: number;
        contenuId: number;
    })[]>;
    findOne(id: number): Promise<({
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
        questions: ({
            choices: {
                id: number;
                text: string;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            text: string;
            quizId: number;
        })[];
    } & {
        id: number;
        contenuId: number;
    }) | null>;
    getQuizWithQuestions(contenuId: number): Promise<({
        choices: {
            id: number;
            text: string;
            isCorrect: boolean;
            questionId: number;
        }[];
    } & {
        id: number;
        text: string;
        quizId: number;
    })[]>;
}
