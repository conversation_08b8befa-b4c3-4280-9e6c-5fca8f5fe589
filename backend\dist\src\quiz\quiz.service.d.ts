import { CreateQuizDto } from './dto/create-quiz.dto';
import { PrismaService } from 'nestjs-prisma';
export declare class QuizService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createQuizDto: CreateQuizDto): Promise<{
        message: string;
        quizId: number;
    }>;
    findAll(): Promise<({
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            published: boolean;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    })[]>;
    findOne(id: number): Promise<({
        contenu: {
            id: number;
            type: import(".prisma/client").$Enums.ContenuType;
            title: string;
            published: boolean;
            fileUrl: string | null;
            fileType: import(".prisma/client").$Enums.FileType | null;
        };
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    }) | null>;
    getQuizWithQuestions(contenuId: number): Promise<{
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    }>;
    updateByContenuId(contenuId: number, data: {
        timeLimit: number;
        questions: any[];
    }): Promise<{
        questions: ({
            choices: {
                id: number;
                imageUrl: string | null;
                text: string | null;
                isCorrect: boolean;
                questionId: number;
            }[];
        } & {
            id: number;
            type: import(".prisma/client").$Enums.QuestionType;
            imageUrl: string | null;
            text: string;
            score: number;
            negativeMark: number;
            correctText: string | null;
            quizId: number;
        })[];
    } & {
        id: number;
        description: string | null;
        title: string | null;
        contenuId: number;
        timeLimit: number | null;
    }>;
}
