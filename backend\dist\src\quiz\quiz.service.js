"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuizService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let QuizService = class QuizService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createQuizDto) {
        const { contenuId, title, description, timeLimit, questions } = createQuizDto;
        const quiz = await this.prisma.quiz.create({
            data: {
                contenu: { connect: { id: contenuId } },
                title,
                description,
                timeLimit,
            },
        });
        for (const question of questions) {
            const createdQuestion = await this.prisma.question.create({
                data: {
                    text: question.text,
                    imageUrl: question.imageUrl,
                    type: question.type,
                    score: question.score,
                    negativeMark: question.negativeMark || 0,
                    correctText: question.correctText,
                    quizId: quiz.id,
                },
            });
            if (['MCQ', 'IMAGE_CHOICE', 'TRUE_FALSE'].includes(question.type)) {
                for (const choice of question.choices || []) {
                    await this.prisma.choice.create({
                        data: {
                            text: choice.text,
                            imageUrl: choice.imageUrl,
                            isCorrect: !!choice.isCorrect,
                            questionId: createdQuestion.id,
                        },
                    });
                }
            }
        }
        return { message: 'Quiz created with advanced types ✅', quizId: quiz.id };
    }
    async findAll() {
        return this.prisma.quiz.findMany({
            include: {
                contenu: true,
                questions: {
                    include: {
                        choices: true,
                    },
                },
            },
        });
    }
    async findOne(id) {
        return this.prisma.quiz.findUnique({
            where: { id },
            include: {
                contenu: true,
                questions: {
                    include: {
                        choices: true,
                    },
                },
            },
        });
    }
    async getQuizWithQuestions(contenuId) {
        const quiz = await this.prisma.quiz.findUnique({
            where: { contenuId },
            include: {
                questions: {
                    include: {
                        choices: true,
                    },
                },
            },
        });
        if (!quiz) {
            throw new Error("Quiz not found for this contenu.");
        }
        return quiz;
    }
    async updateByContenuId(contenuId, data) {
        const existingQuiz = await this.prisma.quiz.findUnique({
            where: { contenuId },
            include: {
                questions: {
                    include: { choices: true },
                },
            },
        });
        if (!existingQuiz) {
            throw new Error("Quiz introuvable pour ce contenu.");
        }
        const questionIds = existingQuiz.questions.map((q) => q.id);
        await this.prisma.answer.deleteMany({
            where: { questionId: { in: questionIds } },
        });
        await this.prisma.choice.deleteMany({
            where: { questionId: { in: questionIds } },
        });
        await this.prisma.question.deleteMany({
            where: { quizId: existingQuiz.id },
        });
        for (const question of data.questions) {
            const createdQuestion = await this.prisma.question.create({
                data: {
                    text: question.text,
                    type: question.type,
                    score: question.score,
                    negativeMark: question.negativeMark || 0,
                    correctText: question.correctText || null,
                    imageUrl: question.imageUrl || null,
                    quizId: existingQuiz.id,
                },
            });
            if (['MCQ', 'IMAGE_CHOICE', 'TRUE_FALSE'].includes(question.type)) {
                for (const choice of question.choices || []) {
                    await this.prisma.choice.create({
                        data: {
                            text: choice.text || null,
                            imageUrl: choice.imageUrl || null,
                            isCorrect: !!choice.isCorrect,
                            questionId: createdQuestion.id,
                        },
                    });
                }
            }
        }
        return this.prisma.quiz.update({
            where: { id: existingQuiz.id },
            data: {
                timeLimit: data.timeLimit,
            },
            include: {
                questions: {
                    include: { choices: true },
                },
            },
        });
    }
};
exports.QuizService = QuizService;
exports.QuizService = QuizService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], QuizService);
//# sourceMappingURL=quiz.service.js.map