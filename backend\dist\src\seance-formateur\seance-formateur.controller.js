"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeanceFormateurController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const seance_formateur_service_1 = require("./seance-formateur.service");
const allowedExtensions = ['.jpg', '.jpeg', '.png', '.mp4'];
const storage = (0, multer_1.diskStorage)({
    destination: './uploads/seance-media',
    filename: (req, file, cb) => {
        const ext = (0, path_1.extname)(file.originalname).toLowerCase();
        if (!allowedExtensions.includes(ext)) {
            return cb(new Error('Type de fichier non supporté'), '');
        }
        const unique = `${Date.now()}-${Math.round(Math.random() * 1e9)}${ext}`;
        cb(null, unique);
    },
});
let SeanceFormateurController = class SeanceFormateurController {
    service;
    constructor(service) {
        this.service = service;
    }
    async uploadMediaToSeance(seanceId, file, body) {
        const { type } = body;
        if (!file)
            throw new Error('Aucun fichier reçu');
        return this.service.addMediaToSeance({
            seanceId: Number(seanceId),
            type,
            fileUrl: `http://localhost:8000/uploads/seance-media/${file.filename}`,
        });
    }
    async getMediaForSeance(seanceId) {
        return this.service.getMediaForSeance(Number(seanceId));
    }
    async removeMedia(mediaId) {
        return this.service.removeMedia(Number(mediaId));
    }
    async create(body, req) {
        const formateurId = req.user?.id || body.formateurId;
        return this.service.create(body, formateurId);
    }
    async findAll() {
        return this.service.findAll();
    }
    async findByFormateur(id) {
        return this.service.findByFormateur(+id);
    }
    async findOne(id) {
        return this.service.findOne(+id);
    }
    async remove(id) {
        return this.service.remove(+id);
    }
    async getDetails(id) {
        return this.service.getProgramDetails(+id);
    }
    async getProgramsByFormateur(formateurId) {
        return this.service.getProgramsByFormateur(+formateurId);
    }
};
exports.SeanceFormateurController = SeanceFormateurController;
__decorate([
    (0, common_1.Post)(':id/upload-media'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', { storage })),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "uploadMediaToSeance", null);
__decorate([
    (0, common_1.Get)(':id/media'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "getMediaForSeance", null);
__decorate([
    (0, common_1.Delete)('media/:mediaId'),
    __param(0, (0, common_1.Param)('mediaId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "removeMedia", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('formateur/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "findByFormateur", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "findOne", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('details/:buildProgramId'),
    __param(0, (0, common_1.Param)('buildProgramId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "getDetails", null);
__decorate([
    (0, common_1.Get)('programs-by-formateur/:formateurId'),
    __param(0, (0, common_1.Param)('formateurId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFormateurController.prototype, "getProgramsByFormateur", null);
exports.SeanceFormateurController = SeanceFormateurController = __decorate([
    (0, common_1.Controller)('seance-formateur'),
    __metadata("design:paramtypes", [seance_formateur_service_1.SeanceFormateurService])
], SeanceFormateurController);
//# sourceMappingURL=seance-formateur.controller.js.map