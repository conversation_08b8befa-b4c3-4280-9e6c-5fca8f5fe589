import { PrismaService } from 'nestjs-prisma';
export declare class SeanceFormateurService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: any, formateurId: number): Promise<{
        message: string;
        seanceId: number;
    }>;
    findAll(): Promise<({
        buildProgram: {
            program: {
                id: number;
                name: string;
                published: boolean;
            };
        } & {
            id: number;
            createdAt: Date | null;
            level: string;
            programId: number;
            startDate: Date | null;
            endDate: Date | null;
            imageUrl: string | null;
        };
        formateur: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        title: string;
        buildProgramId: number;
        startTime: Date;
        formateurId: number;
    })[]>;
    findByFormateur(formateurId: number): Promise<({
        buildProgram: {
            program: {
                id: number;
                name: string;
                published: boolean;
            };
        } & {
            id: number;
            createdAt: Date | null;
            level: string;
            programId: number;
            startDate: Date | null;
            endDate: Date | null;
            imageUrl: string | null;
        };
    } & {
        id: number;
        createdAt: Date;
        title: string;
        buildProgramId: number;
        startTime: Date;
        formateurId: number;
    })[]>;
    findOne(id: number): Promise<({
        buildProgram: {
            program: {
                id: number;
                name: string;
                published: boolean;
            };
        } & {
            id: number;
            createdAt: Date | null;
            level: string;
            programId: number;
            startDate: Date | null;
            endDate: Date | null;
            imageUrl: string | null;
        };
        formateur: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    } & {
        id: number;
        createdAt: Date;
        title: string;
        buildProgramId: number;
        startTime: Date;
        formateurId: number;
    }) | null>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getProgramDetails(buildProgramId: number): Promise<{
        program: {
            name: string;
        };
        modules: {
            id: number;
            courses: {
                id: number;
                contenus: {
                    id: number;
                    contenu: {
                        title: string;
                        fileUrl: string | null;
                    };
                }[];
                course: {
                    title: string;
                };
            }[];
            module: {
                name: string;
            };
        }[];
        seancesFormateur: {
            id: number;
            title: string;
            formateur: {
                id: number;
                name: string | null;
            };
            startTime: Date;
        }[];
    } | null>;
    getProgramsByFormateur(formateurId: number): Promise<({
        program: {
            id: number;
            name: string;
            published: boolean;
        };
        modules: ({
            courses: ({
                contenus: ({
                    contenu: {
                        id: number;
                        type: import(".prisma/client").$Enums.ContenuType;
                        title: string;
                        published: boolean;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                    };
                } & {
                    id: number;
                    contenuId: number;
                    buildProgramCourseId: number;
                })[];
                course: {
                    id: number;
                    title: string;
                };
            } & {
                id: number;
                courseId: number;
                buildProgramModuleId: number;
            })[];
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
        } & {
            id: number;
            moduleId: number;
            buildProgramId: number;
        })[];
    } & {
        id: number;
        createdAt: Date | null;
        level: string;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    })[]>;
    addMediaToSeance({ seanceId, type, fileUrl }: {
        seanceId: any;
        type: any;
        fileUrl: any;
    }): Promise<{
        id: number;
        createdAt: Date;
        type: import(".prisma/client").$Enums.FileType;
        fileUrl: string;
        seanceId: number;
    }>;
    getMediaForSeance(seanceId: number): Promise<{
        id: number;
        createdAt: Date;
        type: import(".prisma/client").$Enums.FileType;
        fileUrl: string;
        seanceId: number;
    }[]>;
    removeMedia(id: number): Promise<{
        id: number;
        createdAt: Date;
        type: import(".prisma/client").$Enums.FileType;
        fileUrl: string;
        seanceId: number;
    }>;
}
