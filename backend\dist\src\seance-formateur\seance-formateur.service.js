"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeanceFormateurService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let SeanceFormateurService = class SeanceFormateurService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data, formateurId) {
        const { title, startTime, buildProgramId } = data;
        const seance = await this.prisma.seanceFormateur.create({
            data: {
                title,
                startTime: new Date(startTime),
                buildProgramId: Number(buildProgramId),
                formateurId,
            },
        });
        return { message: 'Séance créée ✅', seanceId: seance.id };
    }
    async findAll() {
        return this.prisma.seanceFormateur.findMany({
            include: {
                formateur: true,
                buildProgram: {
                    include: { program: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findByFormateur(formateurId) {
        return this.prisma.seanceFormateur.findMany({
            where: { formateurId },
            include: {
                buildProgram: {
                    include: { program: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOne(id) {
        return this.prisma.seanceFormateur.findUnique({
            where: { id },
            include: {
                formateur: true,
                buildProgram: {
                    include: { program: true },
                },
            },
        });
    }
    async remove(id) {
        await this.prisma.seanceFormateur.delete({ where: { id } });
        return { message: 'Séance supprimée 🗑️' };
    }
    async getProgramDetails(buildProgramId) {
        return this.prisma.buildProgram.findUnique({
            where: { id: buildProgramId },
            select: {
                program: {
                    select: { name: true },
                },
                seancesFormateur: {
                    select: {
                        id: true,
                        title: true,
                        startTime: true,
                        formateur: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
                modules: {
                    select: {
                        id: true,
                        module: {
                            select: { name: true },
                        },
                        courses: {
                            select: {
                                id: true,
                                course: {
                                    select: { title: true },
                                },
                                contenus: {
                                    select: {
                                        id: true,
                                        contenu: {
                                            select: {
                                                title: true,
                                                fileUrl: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
    }
    async getProgramsByFormateur(formateurId) {
        const seances = await this.prisma.seanceFormateur.findMany({
            where: { formateurId },
            select: { buildProgramId: true },
        });
        const buildProgramIds = Array.from(new Set(seances.map((s) => s.buildProgramId)));
        return this.prisma.buildProgram.findMany({
            where: { id: { in: buildProgramIds } },
            include: {
                program: true,
                modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: { contenu: true },
                                },
                            },
                        },
                    },
                },
            },
        });
    }
    async addMediaToSeance({ seanceId, type, fileUrl }) {
        return this.prisma.seanceMedia.create({
            data: { seanceId, type, fileUrl },
        });
    }
    async getMediaForSeance(seanceId) {
        return this.prisma.seanceMedia.findMany({ where: { seanceId } });
    }
    async removeMedia(id) {
        return this.prisma.seanceMedia.delete({ where: { id } });
    }
};
exports.SeanceFormateurService = SeanceFormateurService;
exports.SeanceFormateurService = SeanceFormateurService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], SeanceFormateurService);
//# sourceMappingURL=seance-formateur.service.js.map