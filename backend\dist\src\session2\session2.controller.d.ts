import { Session2Service } from './session2.service';
export declare class Session2Controller {
    private readonly service;
    constructor(service: Session2Service);
    create(file: Express.Multer.File, body: any): Promise<{
        message: string;
    }>;
    findAll(): Promise<({
        program: {
            id: number;
            name: string;
            published: boolean;
        };
        session2Modules: ({
            courses: ({
                contenus: ({
                    contenu: {
                        id: number;
                        type: import(".prisma/client").$Enums.ContenuType;
                        title: string;
                        published: boolean;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                    };
                } & {
                    id: number;
                    contenuId: number;
                    session2CourseId: number;
                })[];
                course: {
                    id: number;
                    title: string;
                };
            } & {
                id: number;
                courseId: number;
                session2ModuleId: number;
            })[];
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
        } & {
            id: number;
            moduleId: number;
            session2Id: number;
        })[];
    } & {
        id: number;
        name: string;
        createdAt: Date;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    })[]>;
    remove(id: string): Promise<{
        id: number;
        name: string;
        createdAt: Date;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    }>;
}
