"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Session2Controller = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const session2_service_1 = require("./session2.service");
const storage = (0, multer_1.diskStorage)({
    destination: './uploads/sessions',
    filename: (req, file, cb) => {
        const uniqueName = `${Date.now()}${(0, path_1.extname)(file.originalname)}`;
        cb(null, uniqueName);
    },
});
let Session2Controller = class Session2Controller {
    service;
    constructor(service) {
        this.service = service;
    }
    async create(file, body) {
        return this.service.create(body, file);
    }
    findAll() {
        return this.service.findAll();
    }
    remove(id) {
        return this.service.remove(+id);
    }
};
exports.Session2Controller = Session2Controller;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('image', { storage })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], Session2Controller.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Session2Controller.prototype, "findAll", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], Session2Controller.prototype, "remove", null);
exports.Session2Controller = Session2Controller = __decorate([
    (0, common_1.Controller)('session2'),
    __metadata("design:paramtypes", [session2_service_1.Session2Service])
], Session2Controller);
//# sourceMappingURL=session2.controller.js.map