"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Session2Module = void 0;
const common_1 = require("@nestjs/common");
const session2_service_1 = require("./session2.service");
const session2_controller_1 = require("./session2.controller");
let Session2Module = class Session2Module {
};
exports.Session2Module = Session2Module;
exports.Session2Module = Session2Module = __decorate([
    (0, common_1.Module)({
        controllers: [session2_controller_1.Session2Controller],
        providers: [session2_service_1.Session2Service],
    })
], Session2Module);
//# sourceMappingURL=session2.module.js.map