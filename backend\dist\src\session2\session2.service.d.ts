import { PrismaService } from 'nestjs-prisma';
export declare class Session2Service {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(data: any, file?: Express.Multer.File): Promise<{
        message: string;
    }>;
    remove(id: number): Promise<{
        id: number;
        name: string;
        createdAt: Date;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    }>;
    findAll(): Promise<({
        program: {
            id: number;
            name: string;
            published: boolean;
        };
        session2Modules: ({
            courses: ({
                contenus: ({
                    contenu: {
                        id: number;
                        type: import(".prisma/client").$Enums.ContenuType;
                        title: string;
                        published: boolean;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                    };
                } & {
                    id: number;
                    contenuId: number;
                    session2CourseId: number;
                })[];
                course: {
                    id: number;
                    title: string;
                };
            } & {
                id: number;
                courseId: number;
                session2ModuleId: number;
            })[];
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
        } & {
            id: number;
            moduleId: number;
            session2Id: number;
        })[];
    } & {
        id: number;
        name: string;
        createdAt: Date;
        programId: number;
        startDate: Date | null;
        endDate: Date | null;
        imageUrl: string | null;
    })[]>;
}
