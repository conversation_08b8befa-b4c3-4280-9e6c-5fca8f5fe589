"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Session2Service = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let Session2Service = class Session2Service {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data, file) {
        const { name, programId, startDate, endDate } = data;
        const programStructure = await this.prisma.buildProgram.findFirst({
            where: { programId: Number(programId) },
            include: {
                modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: { contenu: true },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!programStructure) {
            throw new Error("Structure du programme introuvable.");
        }
        const session2 = await this.prisma.session2.create({
            data: {
                name,
                programId: Number(programId),
                startDate: startDate ? new Date(startDate) : undefined,
                endDate: endDate ? new Date(endDate) : undefined,
                imageUrl: file
                    ? `http://localhost:8000/uploads/sessions/${file.filename}`
                    : undefined,
            },
        });
        for (const mod of programStructure.modules) {
            const s2mod = await this.prisma.session2Module.create({
                data: {
                    session2Id: session2.id,
                    moduleId: mod.moduleId,
                },
            });
            for (const course of mod.courses) {
                const s2course = await this.prisma.session2Course.create({
                    data: {
                        session2ModuleId: s2mod.id,
                        courseId: course.courseId,
                    },
                });
                for (const ct of course.contenus) {
                    await this.prisma.session2Contenu.create({
                        data: {
                            session2CourseId: s2course.id,
                            contenuId: ct.contenuId,
                        },
                    });
                }
            }
        }
        return { message: "✅ Session2 créée avec structure copiée avec succès" };
    }
    async remove(id) {
        return this.prisma.session2.delete({
            where: { id },
        });
    }
    async findAll() {
        return this.prisma.session2.findMany({
            include: {
                program: true,
                session2Modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: {
                                        contenu: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
    }
};
exports.Session2Service = Session2Service;
exports.Session2Service = Session2Service = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], Session2Service);
//# sourceMappingURL=session2.service.js.map