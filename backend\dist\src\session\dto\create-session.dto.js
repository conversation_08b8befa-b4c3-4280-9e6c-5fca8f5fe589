"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSessionDto = exports.CreateSessionModuleDto = exports.CreateSessionCourseDto = exports.CreateSessionContenuDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateSessionContenuDto {
    contenuId;
}
exports.CreateSessionContenuDto = CreateSessionContenuDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateSessionContenuDto.prototype, "contenuId", void 0);
class CreateSessionCourseDto {
    courseId;
    contenus;
}
exports.CreateSessionCourseDto = CreateSessionCourseDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateSessionCourseDto.prototype, "courseId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateSessionContenuDto),
    __metadata("design:type", Array)
], CreateSessionCourseDto.prototype, "contenus", void 0);
class CreateSessionModuleDto {
    moduleId;
    courses;
}
exports.CreateSessionModuleDto = CreateSessionModuleDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateSessionModuleDto.prototype, "moduleId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateSessionCourseDto),
    __metadata("design:type", Array)
], CreateSessionModuleDto.prototype, "courses", void 0);
class CreateSessionDto {
    programId;
    startDate;
    endDate;
    modules;
}
exports.CreateSessionDto = CreateSessionDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateSessionDto.prototype, "programId", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateSessionDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateSessionDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateSessionModuleDto),
    __metadata("design:type", Array)
], CreateSessionDto.prototype, "modules", void 0);
//# sourceMappingURL=create-session.dto.js.map