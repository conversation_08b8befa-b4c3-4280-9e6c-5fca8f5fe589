import { SessionsService } from './session.service';
export declare class SessionsController {
    private readonly sessionsService;
    constructor(sessionsService: SessionsService);
    create(file: Express.Multer.File, body: any): Promise<{
        message: string;
        sessionId: number;
    }>;
    findAll(): Promise<({
        program: {
            id: number;
            name: string;
        };
        modules: ({
            module: {
                id: number;
                name: string;
                periodUnit: import(".prisma/client").$Enums.PeriodUnit;
                duration: number;
            };
            courses: ({
                course: {
                    id: number;
                    title: string;
                };
                contenus: ({
                    contenu: {
                        id: number;
                        type: import(".prisma/client").$Enums.ContenuType;
                        title: string;
                        fileUrl: string | null;
                        fileType: import(".prisma/client").$Enums.FileType | null;
                    };
                } & {
                    id: number;
                    contenuId: number;
                    sessionCourseId: number;
                })[];
            } & {
                id: number;
                courseId: number;
                sessionModuleId: number;
            })[];
        } & {
            id: number;
            moduleId: number;
            sessionId: number;
        })[];
    } & {
        id: number;
        createdAt: Date;
        programId: number;
        startDate: Date;
        endDate: Date;
        imageUrl: string | null;
    })[]>;
    remove(id: string): Promise<{
        id: number;
        createdAt: Date;
        programId: number;
        startDate: Date;
        endDate: Date;
        imageUrl: string | null;
    }>;
}
