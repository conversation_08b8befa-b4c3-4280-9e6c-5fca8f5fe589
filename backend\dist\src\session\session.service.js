"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionsService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let SessionsService = class SessionsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(data) {
        const { programId, startDate, endDate, modules, imageUrl } = data;
        const parsedModules = JSON.parse(modules);
        const session = await this.prisma.session.create({
            data: {
                programId: Number(programId),
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                imageUrl,
            },
        });
        for (const mod of parsedModules) {
            const sessionModule = await this.prisma.sessionModule.create({
                data: {
                    sessionId: session.id,
                    moduleId: mod.moduleId,
                },
            });
            for (const course of mod.courses) {
                const sessionCourse = await this.prisma.sessionCourse.create({
                    data: {
                        sessionModuleId: sessionModule.id,
                        courseId: course.courseId,
                    },
                });
                for (const contenu of course.contenus) {
                    await this.prisma.sessionContenu.create({
                        data: {
                            sessionCourseId: sessionCourse.id,
                            contenuId: contenu.contenuId,
                        },
                    });
                }
            }
        }
        return { message: 'Session créée avec succès ✅', sessionId: session.id };
    }
    async findAll() {
        return this.prisma.session.findMany({
            include: {
                program: true,
                modules: {
                    include: {
                        module: true,
                        courses: {
                            include: {
                                course: true,
                                contenus: {
                                    include: {
                                        contenu: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
    }
    async remove(id) {
        return this.prisma.session.delete({
            where: { id },
        });
    }
};
exports.SessionsService = SessionsService;
exports.SessionsService = SessionsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], SessionsService);
//# sourceMappingURL=session.service.js.map