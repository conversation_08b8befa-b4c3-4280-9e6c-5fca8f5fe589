{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../../src/session/session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAG5C,iDAA8C;AAKvC,IAAM,eAAe,GAArB,MAAM,eAAe;IACG;IAA7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,MAAM,CAAC,IAAS;QACpB,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;QAElE,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAE1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/C,IAAI,EAAE;gBACJ,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;gBAC9B,OAAO,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC;gBAC1B,QAAQ;aACT;SACF,CAAC,CAAC;QAEH,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;YAChC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC3D,IAAI,EAAE;oBACJ,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,QAAQ,EAAE,GAAG,CAAC,QAAQ;iBACvB;aACF,CAAC,CAAC;YAEH,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBACjC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;oBAC3D,IAAI,EAAE;wBACJ,eAAe,EAAE,aAAa,CAAC,EAAE;wBACjC,QAAQ,EAAE,MAAM,CAAC,QAAQ;qBAC1B;iBACF,CAAC,CAAC;gBAEH,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACtC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;wBACtC,IAAI,EAAE;4BACJ,eAAe,EAAE,aAAa,CAAC,EAAE;4BACjC,SAAS,EAAE,OAAO,CAAC,SAAS;yBAC7B;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,6BAA6B,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;IAC3E,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClC,OAAO,EAAE;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,MAAM,EAAE,IAAI;wBACZ,OAAO,EAAE;4BACP,OAAO,EAAE;gCACP,MAAM,EAAE,IAAI;gCACZ,QAAQ,EAAE;oCACR,OAAO,EAAE;wCACP,OAAO,EAAE,IAAI;qCACd;iCACF;6BACF;yBACF;qBACF;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAChC,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA3EY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAE0B,6BAAa;GADvC,eAAe,CA2E3B"}