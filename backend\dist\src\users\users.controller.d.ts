import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    } | {
        id: number;
        email: string;
        role: import(".prisma/client").$Enums.Role;
        name: string;
        phone: string | null;
        profilePic: null;
        location: string | null;
        skills: string | never[];
        about: string | null;
    }>;
    findAll(): Promise<any[]>;
    getUserById(id: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    }>;
    getUserByEmailAlias(email: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    }>;
    getUserByEmail(email: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    }>;
    updateUserProfile(email: string, file: Express.Multer.File, body: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    }>;
    updateByEmail(email: string, updateUserDto: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    }>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        password: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
        createdAt: Date;
        updatedAt: Date;
        resetToken: string | null;
        resetTokenExpiry: Date | null;
    }>;
    remove(id: string): Promise<any>;
    uploadProfilePic(id: string, file: Express.Multer.File): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string;
        about: string | null;
    }>;
}
