import { PrismaService } from "nestjs-prisma";
import type { CreateUserDto } from "./dto/create-user.dto";
import type { UpdateUserDto } from "./dto/update-user.dto";
import { MailService } from "../mail/mail.service";
export declare class UsersService {
    private readonly prisma;
    private readonly mailService;
    private fallbackUsers;
    constructor(prisma: PrismaService, mailService: MailService);
    private hashPassword;
    private generateTempPassword;
    create(createUserDto: CreateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    findAll(): Promise<any[]>;
    findOne(id: number): Promise<any>;
    update(id: number, updateUserDto: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        isActive: boolean;
    }>;
    toggleUserStatus(id: number, isActive?: boolean): Promise<any>;
    toggleUserStatusByEmail(email: string, isActive?: boolean): Promise<any>;
    remove(id: number): Promise<any>;
    findById(id: number): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        isActive: boolean;
    } | null>;
    findByEmail(email: string): Promise<any>;
    updateByEmail(email: string, updateUserDto: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        isActive: boolean;
    }>;
    updateProfilePic(id: number, profilePicPath: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        isActive: boolean;
    }>;
}
