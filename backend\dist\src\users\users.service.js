"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const bcrypt = require("bcrypt");
const mail_service_1 = require("../mail/mail.service");
let UsersService = class UsersService {
    prisma;
    mailService;
    fallbackUsers = [
        {
            id: 1,
            email: '<EMAIL>',
            role: 'Admin',
            name: 'Khalil Admin',
            phone: null,
            profilePic: null,
            location: null,
            skills: [],
            about: null
        }
    ];
    constructor(prisma, mailService) {
        this.prisma = prisma;
        this.mailService = mailService;
    }
    async hashPassword(password) {
        return bcrypt.hash(password, 10);
    }
    generateTempPassword(length = 10) {
        const upperChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
        const lowerChars = 'abcdefghijkmnopqrstuvwxyz';
        const numbers = '23456789';
        const specialChars = '@#$%&*!?';
        let password = '';
        password += upperChars.charAt(Math.floor(Math.random() * upperChars.length));
        password += lowerChars.charAt(Math.floor(Math.random() * lowerChars.length));
        password += numbers.charAt(Math.floor(Math.random() * numbers.length));
        password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));
        const allChars = upperChars + lowerChars + numbers + specialChars;
        const remainingLength = length - 4;
        for (let i = 0; i < remainingLength; i++) {
            password += allChars.charAt(Math.floor(Math.random() * allChars.length));
        }
        return password.split('').sort(() => 0.5 - Math.random()).join('');
    }
    async create(createUserDto) {
        const tempPassword = this.generateTempPassword();
        console.log("Mot de passe temporaire généré:", tempPassword);
        try {
            console.log("Création d'un nouvel utilisateur:", createUserDto);
            const hashedPassword = await this.hashPassword(tempPassword);
            let formattedSkills;
            if (createUserDto.skills) {
                console.log("Skills avant traitement:", createUserDto.skills);
                console.log("Type de skills:", typeof createUserDto.skills);
                if (typeof createUserDto.skills === 'string') {
                    try {
                        if (createUserDto.skills.startsWith('[') && createUserDto.skills.endsWith(']')) {
                            formattedSkills = JSON.parse(createUserDto.skills);
                            console.log("Skills après parsing JSON:", formattedSkills);
                        }
                        else {
                            formattedSkills = [createUserDto.skills];
                            console.log("Skills convertis en tableau:", formattedSkills);
                        }
                    }
                    catch (e) {
                        console.error('Failed to parse skills:', e);
                        formattedSkills = [];
                    }
                }
                else if (Array.isArray(createUserDto.skills)) {
                    formattedSkills = createUserDto.skills;
                    console.log("Skills est déjà un tableau:", formattedSkills);
                }
                else {
                    console.error("Format de skills non reconnu, conversion en tableau vide");
                    formattedSkills = [];
                }
            }
            else {
                formattedSkills = [];
            }
            const newUser = await this.prisma.user.create({
                data: {
                    email: createUserDto.email,
                    password: hashedPassword,
                    role: createUserDto.role,
                    name: createUserDto.name,
                    phone: createUserDto.phone,
                    location: createUserDto.location,
                    about: createUserDto.about,
                    skills: formattedSkills,
                },
                select: {
                    id: true,
                    email: true,
                    name: true,
                    role: true,
                    phone: true,
                    location: true,
                    about: true,
                    skills: true,
                    profilePic: true,
                },
            });
            console.log("Nouvel utilisateur créé:", newUser);
            try {
                console.log("Envoi de l'email de bienvenue à:", newUser.email);
                await this.mailService.sendWelcomeEmail(newUser.email, tempPassword, newUser.role);
                console.log("Email de bienvenue envoyé avec succès");
            }
            catch (emailError) {
                console.error("Erreur lors de l'envoi de l'email de bienvenue:", emailError);
            }
            return newUser;
        }
        catch (error) {
            console.error("Erreur lors de la création de l'utilisateur:", error);
            console.log('Database error in create, using fallback creation');
            const newUser = {
                id: Math.floor(Math.random() * 1000) + 2,
                email: createUserDto.email,
                role: createUserDto.role,
                name: createUserDto.name || createUserDto.email.split('@')[0],
                phone: createUserDto.phone || null,
                profilePic: null,
                location: createUserDto.location || null,
                skills: createUserDto.skills || [],
                about: createUserDto.about || null
            };
            this.fallbackUsers.push(newUser);
            console.log('Utilisateur ajouté au stockage temporaire. Total:', this.fallbackUsers.length);
            try {
                console.log("Envoi de l'email de bienvenue (fallback) à:", createUserDto.email);
                console.log("Mot de passe temporaire utilisé:", tempPassword);
                await this.mailService.sendWelcomeEmail(createUserDto.email, tempPassword, createUserDto.role);
                console.log("Email de bienvenue (fallback) envoyé avec succès");
            }
            catch (emailError) {
                console.error("Erreur lors de l'envoi de l'email de bienvenue (fallback):", emailError);
                console.log('Email sending failed, but user creation continues');
            }
            return newUser;
        }
    }
    async findAll() {
        try {
            console.log('Attempting to fetch users from database...');
            const users = await this.prisma.user.findMany({
                select: {
                    id: true,
                    email: true,
                    name: true,
                    role: true,
                    phone: true,
                    location: true,
                    about: true,
                    skills: true,
                    profilePic: true,
                },
            });
            console.log('Successfully fetched users from database:', users.length);
            return users;
        }
        catch (error) {
            console.error('Database error in findAll:', error.message);
            console.log('Using fallback data due to database connection issue');
            console.log('Returning fallback users:', this.fallbackUsers.length);
            return this.fallbackUsers;
        }
    }
    async findOne(id) {
        return this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
    }
    async update(id, updateUserDto) {
        return this.prisma.user.update({
            where: { id },
            data: {
                name: updateUserDto.name,
                role: updateUserDto.role,
                phone: updateUserDto.phone,
                location: updateUserDto.location,
                about: updateUserDto.about,
                skills: Array.isArray(updateUserDto.skills)
                    ? updateUserDto.skills
                    : (typeof updateUserDto.skills === 'string'
                        ? [updateUserDto.skills]
                        : undefined),
                profilePic: updateUserDto.profilePic,
            },
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                profilePic: true,
                location: true,
                skills: true,
                about: true,
            },
        });
    }
    async remove(id) {
        try {
            console.log('Attempting to delete user with ID:', id);
            const deletedUser = await this.prisma.user.delete({
                where: { id },
            });
            console.log('User deleted from database successfully:', deletedUser);
            return deletedUser;
        }
        catch (error) {
            console.error('Database error in remove:', error.message);
            console.log('Attempting to delete from fallback storage');
            const userIndex = this.fallbackUsers.findIndex(user => user.id === id);
            if (userIndex === -1) {
                console.error('User not found in fallback storage with ID:', id);
                throw new Error(`User with ID ${id} not found`);
            }
            const deletedUser = this.fallbackUsers.splice(userIndex, 1)[0];
            console.log('User deleted from fallback storage:', deletedUser);
            console.log('Remaining users in fallback storage:', this.fallbackUsers.length);
            return deletedUser;
        }
    }
    async findById(id) {
        try {
            const numericId = parseInt(String(id), 10);
            if (isNaN(numericId))
                throw new Error('ID invalide');
            return this.prisma.user.findUnique({
                where: { id: numericId },
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    profilePic: true,
                    location: true,
                    skills: true,
                    about: true,
                },
            });
        }
        catch (error) {
            console.error('Erreur dans findById:', error);
            throw error;
        }
    }
    async findByEmail(email) {
        return this.prisma.user.findUnique({
            where: { email },
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                profilePic: true,
                location: true,
                skills: true,
                about: true,
            },
        });
    }
    async updateByEmail(email, updateUserDto) {
        try {
            console.log("Mise à jour de l'utilisateur avec email:", email);
            console.log("Données reçues:", updateUserDto);
            if (updateUserDto.skills) {
                console.log("Skills avant traitement:", updateUserDto.skills);
                console.log("Type de skills:", typeof updateUserDto.skills);
                if (typeof updateUserDto.skills === 'string') {
                    try {
                        if (updateUserDto.skills.startsWith('[') && updateUserDto.skills.endsWith(']')) {
                            updateUserDto.skills = JSON.parse(updateUserDto.skills);
                            console.log("Skills après parsing JSON:", updateUserDto.skills);
                        }
                        else {
                            updateUserDto.skills = [updateUserDto.skills];
                            console.log("Skills convertis en tableau:", updateUserDto.skills);
                        }
                    }
                    catch (e) {
                        console.error('Failed to parse skills:', e);
                        updateUserDto.skills = [];
                    }
                }
                else if (Array.isArray(updateUserDto.skills)) {
                    console.log("Skills est déjà un tableau:", updateUserDto.skills);
                }
                else {
                    console.error("Format de skills non reconnu, conversion en tableau vide");
                    updateUserDto.skills = [];
                }
            }
            const updateData = {
                name: updateUserDto.name,
                phone: updateUserDto.phone,
                location: updateUserDto.location,
                about: updateUserDto.about,
            };
            if (updateUserDto.skills !== undefined) {
                updateData.skills = updateUserDto.skills;
            }
            if (updateUserDto.profilePic !== undefined) {
                updateData.profilePic = updateUserDto.profilePic;
            }
            return await this.prisma.user.update({
                where: { email },
                data: updateData,
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    profilePic: true,
                    location: true,
                    skills: true,
                    about: true,
                },
            });
        }
        catch (error) {
            console.error("Erreur dans updateByEmail:", error);
            throw error;
        }
    }
    async updateProfilePic(id, profilePicPath) {
        try {
            return await this.prisma.user.update({
                where: { id },
                data: {
                    profilePic: profilePicPath,
                },
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    profilePic: true,
                    location: true,
                    skills: true,
                    about: true,
                },
            });
        }
        catch (error) {
            console.error("Erreur dans updateProfilePic:", error);
            throw error;
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService,
        mail_service_1.MailService])
], UsersService);
//# sourceMappingURL=users.service.js.map