{"version": 3, "file": "multerconfig.js", "sourceRoot": "", "sources": ["../../../src/utils/multerconfig.ts"], "names": [], "mappings": ";;;AACA,mCAAqC;AACrC,+BAA+B;AAElB,QAAA,aAAa,GAAG;IAC3B,OAAO,EAAE,IAAA,oBAAW,EAAC;QACnB,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;YAChC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;YACxE,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YACrD,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,SAAS,IAAI,YAAY,GAAG,GAAG,EAAE,CAAC;YAC3D,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC3B,CAAC;KACF,CAAC;IACF,MAAM,EAAE;QACN,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;KAC1B;IACD,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC5B,MAAM,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;QAC5E,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,+DAA+D,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/F,CAAC;QACD,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjB,CAAC;CACF,CAAC"}