const express = require('express');
const cors = require('cors');
const app = express();
const port = 8000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data for feedback
let feedbacks = [
  {
    id: 1,
    from: "Etudiant1",
    to: "Formateur1", 
    message: "Great explanation of the module content! The examples were very helpful.",
    timestamp: "2025-05-06T10:15:00",
    type: "etudiant-formateur",
    rating: 4.5,
    category: "course",
    tags: ["Content", "Examples"],
    likes: 3,
    dislikes: 0,
    senderId: 1,
    receiverId: 2,
    createdAt: "2025-05-06T10:15:00",
    updatedAt: "2025-05-06T10:15:00",
    responses: []
  },
  {
    id: 2,
    from: "Formateur1",
    to: "Etudiant2",
    message: "Excellent project work, well done! Your implementation shows deep understanding.",
    timestamp: "2025-05-06T11:30:00", 
    type: "formateur-etudiant",
    rating: 5,
    category: "instructor",
    tags: ["Project", "Implementation"],
    likes: 2,
    dislikes: 0,
    senderId: 2,
    receiverId: 3,
    createdAt: "2025-05-06T11:30:00",
    updatedAt: "2025-05-06T11:30:00",
    responses: []
  },
  {
    id: 3,
    from: "Etudiant3", 
    to: "Admin",
    message: "The platform is sometimes slow to load videos. Could you please look into this?",
    timestamp: "2025-05-07T09:20:00",
    type: "general",
    rating: 3,
    category: "technical", 
    tags: ["Video Quality", "Performance"],
    likes: 5,
    dislikes: 1,
    senderId: 4,
    receiverId: 1,
    createdAt: "2025-05-07T09:20:00",
    updatedAt: "2025-05-07T09:20:00",
    responses: []
  }
];

let nextId = 4;

// Routes
app.get('/feedback', (req, res) => {
  console.log('GET /feedback - Returning feedbacks');
  res.json(feedbacks);
});

app.post('/feedback', (req, res) => {
  console.log('POST /feedback - Creating new feedback:', req.body);
  
  const newFeedback = {
    id: nextId++,
    ...req.body,
    likes: 0,
    dislikes: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    responses: []
  };
  
  feedbacks.unshift(newFeedback);
  res.status(201).json(newFeedback);
});

app.get('/feedback/stats', (req, res) => {
  console.log('GET /feedback/stats');
  
  const totalFeedbacks = feedbacks.length;
  const averageRating = feedbacks.reduce((sum, fb) => sum + fb.rating, 0) / totalFeedbacks;
  
  const categories = {};
  feedbacks.forEach(fb => {
    if (fb.category) {
      categories[fb.category] = (categories[fb.category] || 0) + 1;
    }
  });
  
  const categoryBreakdown = Object.entries(categories).map(([category, count]) => ({
    category,
    count,
    percentage: Math.round((count / totalFeedbacks) * 100)
  }));
  
  res.json({
    totalFeedbacks,
    averageRating: Math.round(averageRating * 10) / 10,
    categoryBreakdown,
    recentFeedbackCount: feedbacks.filter(fb => 
      new Date(fb.createdAt) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length,
    pendingResponses: feedbacks.filter(fb => fb.responses.length === 0).length
  });
});

app.get('/feedback/analytics', (req, res) => {
  console.log('GET /feedback/analytics');
  
  const ratingData = [5, 4, 3, 2, 1].map(rating => ({
    name: `${rating} Stars`,
    count: feedbacks.filter(fb => Math.round(fb.rating) === rating).length
  }));
  
  const categories = {};
  feedbacks.forEach(fb => {
    if (fb.category) {
      categories[fb.category] = (categories[fb.category] || 0) + 1;
    }
  });
  
  const categoryData = Object.entries(categories).map(([name, value]) => ({
    name,
    value
  }));
  
  res.json({
    ratingData,
    categoryData,
    timelineData: []
  });
});

app.post('/feedback/:id/like', (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`POST /feedback/${id}/like`);
  
  const feedback = feedbacks.find(fb => fb.id === id);
  if (feedback) {
    feedback.likes++;
    res.json(feedback);
  } else {
    res.status(404).json({ message: 'Feedback not found' });
  }
});

app.post('/feedback/:id/dislike', (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`POST /feedback/${id}/dislike`);
  
  const feedback = feedbacks.find(fb => fb.id === id);
  if (feedback) {
    feedback.dislikes++;
    res.json(feedback);
  } else {
    res.status(404).json({ message: 'Feedback not found' });
  }
});

app.post('/feedback/:id/report', (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`POST /feedback/${id}/report`);
  
  const feedback = feedbacks.find(fb => fb.id === id);
  if (feedback) {
    res.json({ message: 'Report submitted successfully' });
  } else {
    res.status(404).json({ message: 'Feedback not found' });
  }
});

app.post('/feedback/:id/respond', (req, res) => {
  const id = parseInt(req.params.id);
  console.log(`POST /feedback/${id}/respond:`, req.body);
  
  const feedback = feedbacks.find(fb => fb.id === id);
  if (feedback) {
    const response = {
      id: Date.now(),
      response: req.body.response,
      responderId: req.body.responderId,
      feedbackId: id,
      createdAt: new Date().toISOString()
    };
    
    feedback.responses.push(response);
    res.json(response);
  } else {
    res.status(404).json({ message: 'Feedback not found' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Feedback test server is running' });
});

const server = app.listen(port, () => {
  console.log(`🚀 Feedback test server running on http://localhost:${port}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /feedback - Get all feedbacks`);
  console.log(`   POST /feedback - Create new feedback`);
  console.log(`   GET  /feedback/stats - Get feedback statistics`);
  console.log(`   GET  /feedback/analytics - Get feedback analytics`);
  console.log(`   POST /feedback/:id/like - Like a feedback`);
  console.log(`   POST /feedback/:id/dislike - Dislike a feedback`);
  console.log(`   POST /feedback/:id/report - Report a feedback`);
  console.log(`   POST /feedback/:id/respond - Respond to feedback`);
  console.log(`   GET  /health - Health check`);
  console.log(`\n✅ Server is ready to accept requests!`);
});

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down feedback test server...');
  server.close(() => {
    console.log('✅ Server closed gracefully');
    process.exit(0);
  });
});

// Prevent the process from exiting
process.stdin.resume();
