// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  Etudiant
  Formateur
  Admin
  CreateurDeFormation
  Etablissement
}

model Mail {
  id               String    @id @default(uuid())
  email            String    @unique
  password         String
  resetToken       String?
  resetTokenExpiry DateTime?
}

enum FileType {
  PDF
  IMAGE
  VIDEO
}

enum ContenuType {
  Cours
  Exercice
  Quiz
}

enum PeriodUnit {
  Day
  Week
  Month
}

model User {
  id         Int      @id @default(autoincrement())
  role       Role     @default(Etudiant)
  email      String   @unique
  password   String
  name       String?
  phone      String?
  profilePic String?
  location   String?
  skills     String[]
  about      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  resetToken              String?
  resetTokenExpiry        DateTime?
  formateurs              Formateur[]
  Etudiants               Etudiant[]
  Createurs_De_Formations Createur_De_Formation[]
  Admins                  Admin[]
  Etablissements          Etablissement[]
  ResetToken              ResetToken[]
  UserAnswer              UserAnswer[]
}

model Formateur {
  id         Int    @id @default(autoincrement())
  speciality String
  User       User   @relation(fields: [userId], references: [id])
  userId     Int
}

model Etudiant {
  id                Int    @id @default(autoincrement())
  NameEtablissement String
  User              User   @relation(fields: [userId], references: [id])
  userId            Int
}

model Createur_De_Formation {
  id     Int  @id @default(autoincrement())
  User   User @relation(fields: [userId], references: [id])
  userId Int
}

model Admin {
  id     Int  @id @default(autoincrement())
  User   User @relation(fields: [userId], references: [id])
  userId Int
}

model Etablissement {
  id     Int  @id @default(autoincrement())
  User   User @relation(fields: [userId], references: [id])
  userId Int
}

model ResetToken {
  id         Int      @id @default(autoincrement())
  token      String
  User       User     @relation(fields: [userId], references: [id])
  userId     Int
  expiryDate DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Program {
  id       Int             @id @default(autoincrement())
  name     String
  modules  ProgramModule[]
  sessions Session[]
}

model Module {
  id             Int             @id @default(autoincrement())
  name           String
  periodUnit     PeriodUnit
  duration       Int
  programs       ProgramModule[]
  courses        ModuleCourse[]
  sessionModules SessionModule[]
}

model Course {
  id             Int             @id @default(autoincrement())
  title          String
  modules        ModuleCourse[]
  courseContenus CourseContenu[]
  sessionCourses SessionCourse[]
}

model Contenu {
  id              Int              @id @default(autoincrement())
  title           String
  fileUrl         String? // Optionnel dans le schéma Prisma
  fileType        FileType?
  type            ContenuType
  courseContenus  CourseContenu[] // NEW many-to-many
  sessionContenus SessionContenu[]
  quiz            Quiz?
}

model ProgramModule {
  id        Int     @id @default(autoincrement())
  program   Program @relation(fields: [programId], references: [id], onDelete: Cascade)
  programId Int
  module    Module  @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  moduleId  Int

  @@unique([programId, moduleId])
}

model ModuleCourse {
  id       Int    @id @default(autoincrement())
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  moduleId Int
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId Int

  @@unique([moduleId, courseId])
}

model CourseContenu {
  id        Int     @id @default(autoincrement())
  course    Course  @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId  Int
  contenu   Contenu @relation(fields: [contenuId], references: [id], onDelete: Cascade)
  contenuId Int

  @@unique([courseId, contenuId])
}

model Session {
  id        Int             @id @default(autoincrement())
  program   Program         @relation(fields: [programId], references: [id], onDelete: Cascade)
  programId Int
  startDate DateTime
  endDate   DateTime
  imageUrl  String?
  modules   SessionModule[]
  createdAt DateTime        @default(now())
}

model SessionModule {
  id        Int             @id @default(autoincrement())
  session   Session         @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  sessionId Int
  module    Module          @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  moduleId  Int
  courses   SessionCourse[]

  @@unique([sessionId, moduleId])
}

model SessionCourse {
  id              Int              @id @default(autoincrement())
  sessionModule   SessionModule    @relation(fields: [sessionModuleId], references: [id], onDelete: Cascade)
  sessionModuleId Int
  course          Course           @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId        Int
  contenus        SessionContenu[]

  @@unique([sessionModuleId, courseId])
}

model SessionContenu {
  id              Int           @id @default(autoincrement())
  sessionCourse   SessionCourse @relation(fields: [sessionCourseId], references: [id], onDelete: Cascade)
  sessionCourseId Int
  contenu         Contenu       @relation(fields: [contenuId], references: [id], onDelete: Cascade)
  contenuId       Int

  @@unique([sessionCourseId, contenuId])
}

model Quiz {
  id          Int          @id @default(autoincrement())
  contenu     Contenu      @relation(fields: [contenuId], references: [id], onDelete: Cascade)
  contenuId   Int          @unique
  questions   Question[]
  userAnswers UserAnswer[]
}

model Question {
  id      Int      @id @default(autoincrement())
  text    String
  quiz    Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId  Int
  choices Choice[]
  Answer  Answer[]
}

model Choice {
  id         Int      @id @default(autoincrement())
  text       String
  isCorrect  Boolean  @default(false)
  question   Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  questionId Int
  Answer     Answer[]
}

model UserAnswer {
  id          Int      @id @default(autoincrement())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  quiz        Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId      Int
  score       Int
  submittedAt DateTime @default(now())
  answers     Answer[]
}

model Answer {
  id           Int        @id @default(autoincrement())
  userAnswer   UserAnswer @relation(fields: [userAnswerId], references: [id], onDelete: Cascade)
  userAnswerId Int
  question     Question   @relation(fields: [questionId], references: [id], onDelete: Cascade)
  questionId   Int
  selected     Choice     @relation(fields: [selectedId], references: [id], onDelete: Cascade)
  selectedId   Int
}
