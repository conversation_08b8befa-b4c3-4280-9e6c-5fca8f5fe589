// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // directUrl=env("DIRECT_URL")
}

enum Role {
  Etudiant
  Formateur
  Admin
  CreateurDeFormation
  Etablissement
}

model Mail {
  id               String    @id @default(uuid())
  email            String    @unique
  password         String
  resetToken       String?
  resetTokenExpiry DateTime?
}

enum FileType {
  PDF
  IMAGE
  VIDEO
  WORD
  EXCEL
  PPT
}

enum ContenuType {
  Cours
  Exercice
  Quiz
}

enum PeriodUnit {
  Day
  Week
  Month
}

model User {
  id         Int      @id @default(autoincrement())
  role       Role     @default(Etudiant)
  email      String   @unique
  password   String
  name       String?
  phone      String?
  profilePic String?
  location   String?
  skills     String[]
  about      String?
  isActive   Boolean  @default(true)

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  isVerified Boolean  @default(false)

  needsVerification Boolean @default(true) // ✅ ADD THIS LINE

  // 🔐 Ajouts pour vérification email :
  emailVerified         DateTime?
  emailVerificationCode String?
  codeExpiryDate        DateTime?

  resetToken              String?
  resetTokenExpiry        DateTime?
  formateurs              Formateur[]
  Etudiants               Etudiant[]
  Createurs_De_Formations Createur_De_Formation[]
  Admins                  Admin[]
  Etablissements          Etablissement[]
  ResetToken              ResetToken[]
  UserAnswer              UserAnswer[]
  SeanceFormateur         SeanceFormateur[]
  sentFeedback            Feedback[]              @relation("SentFeedback")
  receivedFeedback        Feedback[]              @relation("ReceivedFeedback")
  feedbackResponses       FeedbackResponse[]
}

model Formateur {
  id         Int    @id @default(autoincrement())
  speciality String
  User       User   @relation(fields: [userId], references: [id])
  userId     Int
}

model Etudiant {
  id                Int    @id @default(autoincrement())
  NameEtablissement String
  User              User   @relation(fields: [userId], references: [id])
  userId            Int
}

model Createur_De_Formation {
  id     Int  @id @default(autoincrement())
  User   User @relation(fields: [userId], references: [id])
  userId Int
}

model Admin {
  id     Int  @id @default(autoincrement())
  User   User @relation(fields: [userId], references: [id])
  userId Int
}

model Etablissement {
  id     Int  @id @default(autoincrement())
  User   User @relation(fields: [userId], references: [id])
  userId Int
}

model ResetToken {
  id         Int      @id @default(autoincrement())
  token      String
  User       User     @relation(fields: [userId], references: [id])
  userId     Int
  expiryDate DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Program {
  id        Int     @id @default(autoincrement())
  name      String
  published Boolean @default(false)

  modules      ProgramModule[]
  buildProgram buildProgram[]
  sessions2    Session2[]
}

model Module {
  id                  Int                  @id @default(autoincrement())
  name                String
  periodUnit          PeriodUnit
  duration            Int
  programs            ProgramModule[]
  courses             ModuleCourse[]
  buildProgramModules buildProgramModule[]
  session2Modules     Session2Module[]
}

model Course {
  id                  Int                  @id @default(autoincrement())
  title               String
  modules             ModuleCourse[]
  courseContenus      CourseContenu[]
  buildProgramCourses buildProgramCourse[]
  session2Courses     Session2Course[]
}

model Contenu {
  id                   Int                   @id @default(autoincrement())
  title                String
  fileUrl              String?
  fileType             FileType?
  type                 ContenuType
  courseContenus       CourseContenu[] // NEW many-to-many
  buildProgramContenus buildProgramContenu[]
  session2Contenus     Session2Contenu[]
  quiz                 Quiz?
  published            Boolean               @default(false)
}

model ProgramModule {
  id        Int     @id @default(autoincrement())
  program   Program @relation(fields: [programId], references: [id], onDelete: Cascade)
  programId Int
  module    Module  @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  moduleId  Int

  @@unique([programId, moduleId])
}

model ModuleCourse {
  id       Int    @id @default(autoincrement())
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  moduleId Int
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId Int

  @@unique([moduleId, courseId])
}

model CourseContenu {
  id        Int     @id @default(autoincrement())
  course    Course  @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId  Int
  contenu   Contenu @relation(fields: [contenuId], references: [id], onDelete: Cascade)
  contenuId Int

  @@unique([courseId, contenuId])
}

model buildProgram {
  id        Int       @id @default(autoincrement())
  program   Program   @relation(fields: [programId], references: [id], onDelete: Cascade)
  programId Int
  startDate DateTime?
  endDate   DateTime?
  imageUrl  String?
  level     String    @default("Basique")

  modules          buildProgramModule[]
  seancesFormateur SeanceFormateur[]

  createdAt DateTime? @default(now())
}

model buildProgramModule {
  id             Int                  @id @default(autoincrement())
  buildProgram   buildProgram         @relation(fields: [buildProgramId], references: [id], onDelete: Cascade)
  buildProgramId Int
  module         Module               @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  moduleId       Int
  courses        buildProgramCourse[]

  @@unique([buildProgramId, moduleId])
}

model buildProgramCourse {
  id                   Int                   @id @default(autoincrement())
  buildProgramModule   buildProgramModule    @relation(fields: [buildProgramModuleId], references: [id], onDelete: Cascade)
  buildProgramModuleId Int
  course               Course                @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId             Int
  contenus             buildProgramContenu[]

  @@unique([buildProgramModuleId, courseId])
}

model buildProgramContenu {
  id                   Int                @id @default(autoincrement())
  buildProgramCourse   buildProgramCourse @relation(fields: [buildProgramCourseId], references: [id], onDelete: Cascade)
  buildProgramCourseId Int
  contenu              Contenu            @relation(fields: [contenuId], references: [id], onDelete: Cascade)
  contenuId            Int

  @@unique([buildProgramCourseId, contenuId])
}

enum QuestionType {
  MCQ
  TRUE_FALSE
  FILL_BLANK
  IMAGE_CHOICE
}

model Quiz {
  id          Int          @id @default(autoincrement())
  contenu     Contenu      @relation(fields: [contenuId], references: [id], onDelete: Cascade)
  contenuId   Int          @unique
  title       String?
  description String?
  timeLimit   Int? // in seconds (e.g., 600 for 10 mins)
  questions   Question[]
  userAnswers UserAnswer[]
}

model Question {
  id           Int          @id @default(autoincrement())
  text         String
  imageUrl     String? // optional image with question
  type         QuestionType @default(MCQ)
  score        Int          @default(1) // points for correct answer
  negativeMark Int          @default(0) // optional penalty
  quiz         Quiz         @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId       Int
  choices      Choice[] // only for MCQ and IMAGE_CHOICE
  correctText  String? // only for FILL_BLANK
  answers      Answer[]
}

model Choice {
  id         Int      @id @default(autoincrement())
  text       String?
  imageUrl   String? // used for image-based answers
  isCorrect  Boolean  @default(false)
  question   Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  questionId Int
  answers    Answer[]
}

model UserAnswer {
  id          Int      @id @default(autoincrement())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  quiz        Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId      Int
  score       Int
  submittedAt DateTime @default(now())
  answers     Answer[]
}

model Answer {
  id           Int        @id @default(autoincrement())
  userAnswer   UserAnswer @relation(fields: [userAnswerId], references: [id], onDelete: Cascade)
  userAnswerId Int
  question     Question   @relation(fields: [questionId], references: [id], onDelete: Cascade)
  questionId   Int
  selectedId   Int? // for MCQ/IMAGE_CHOICE
  selected     Choice?    @relation(fields: [selectedId], references: [id])
  textAnswer   String? // for FILL_BLANK
}

model Session2 {
  id              Int              @id @default(autoincrement())
  name            String
  program         Program          @relation(fields: [programId], references: [id], onDelete: Cascade)
  programId       Int
  startDate       DateTime?
  endDate         DateTime?
  imageUrl        String?
  createdAt       DateTime         @default(now())
  session2Modules Session2Module[]
}

model Session2Module {
  id         Int      @id @default(autoincrement())
  session2Id Int
  session2   Session2 @relation(fields: [session2Id], references: [id], onDelete: Cascade)

  moduleId Int
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  courses Session2Course[]
}

model Session2Course {
  id               Int            @id @default(autoincrement())
  session2ModuleId Int
  session2Module   Session2Module @relation(fields: [session2ModuleId], references: [id], onDelete: Cascade)

  courseId Int
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  contenus Session2Contenu[]

  @@unique([session2ModuleId, courseId])
}

model Session2Contenu {
  id               Int            @id @default(autoincrement())
  session2CourseId Int
  session2Course   Session2Course @relation(fields: [session2CourseId], references: [id], onDelete: Cascade)

  contenuId Int
  contenu   Contenu @relation(fields: [contenuId], references: [id], onDelete: Cascade)

  @@unique([session2CourseId, contenuId])
}

model SeanceFormateur {
  id        Int      @id @default(autoincrement())
  title     String
  startTime DateTime

  formateur   User @relation(fields: [formateurId], references: [id], onDelete: Cascade)
  formateurId Int

  buildProgram   buildProgram  @relation(fields: [buildProgramId], references: [id], onDelete: Cascade)
  buildProgramId Int
  medias         SeanceMedia[]

  createdAt DateTime @default(now())
}

model SeanceMedia {
  id        Int      @id @default(autoincrement())
  seanceId  Int
  type      FileType
  fileUrl   String
  createdAt DateTime @default(now())

  seance SeanceFormateur @relation(fields: [seanceId], references: [id], onDelete: Cascade)
}

model Feedback {
  id        Int      @id @default(autoincrement())
  message   String
  type      String?
  rating    Float    @default(0)
  category  String?
  tags      String[]
  likes     Int      @default(0)
  dislikes  Int      @default(0)

  sender     User?   @relation("SentFeedback", fields: [senderId], references: [id])
  senderId   Int?

  receiver   User?   @relation("ReceivedFeedback", fields: [receiverId], references: [id])
  receiverId Int?

  responses  FeedbackResponse[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model FeedbackResponse {
  id         Int      @id @default(autoincrement())
  response   String

  feedback   Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  feedbackId Int

  responder  User?    @relation(fields: [responderId], references: [id])
  responderId Int?

  createdAt  DateTime @default(now())
}
