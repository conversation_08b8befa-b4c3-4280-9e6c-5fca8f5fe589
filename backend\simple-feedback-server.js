const http = require('http');
const url = require('url');

const port = 8001;

// Mock feedback data
const feedbacks = [
  {
    id: 1,
    from: "Etudiant1",
    to: "Formateur1", 
    message: "Great explanation of the module content! The examples were very helpful.",
    timestamp: "2025-05-06T10:15:00",
    type: "etudiant-formateur",
    rating: 4.5,
    category: "course",
    tags: ["Content", "Examples"],
    likes: 3,
    dislikes: 0,
    responses: []
  },
  {
    id: 2,
    from: "Formateur1",
    to: "Etudiant2",
    message: "Excellent project work, well done! Your implementation shows deep understanding.",
    timestamp: "2025-05-06T11:30:00", 
    type: "formateur-etudiant",
    rating: 5,
    category: "instructor",
    tags: ["Project", "Implementation"],
    likes: 2,
    dislikes: 0,
    responses: []
  },
  {
    id: 3,
    from: "Etudiant3", 
    to: "Admin",
    message: "The platform is sometimes slow to load videos. Could you please look into this?",
    timestamp: "2025-05-07T09:20:00",
    type: "general",
    rating: 3,
    category: "technical", 
    tags: ["Video Quality", "Performance"],
    likes: 5,
    dislikes: 1,
    responses: []
  }
];

let nextId = 4;

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  res.setHeader('Content-Type', 'application/json');

  if (path === '/feedback' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify(feedbacks));
  } 
  else if (path === '/feedback' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const newFeedback = JSON.parse(body);
        newFeedback.id = nextId++;
        newFeedback.likes = 0;
        newFeedback.dislikes = 0;
        newFeedback.responses = [];
        newFeedback.timestamp = new Date().toISOString();
        
        feedbacks.unshift(newFeedback);
        
        res.writeHead(201);
        res.end(JSON.stringify(newFeedback));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
  }
  else if (path === '/feedback/stats' && method === 'GET') {
    const stats = {
      totalFeedbacks: feedbacks.length,
      averageRating: feedbacks.reduce((sum, fb) => sum + fb.rating, 0) / feedbacks.length,
      categoryBreakdown: [],
      recentFeedbackCount: feedbacks.length,
      pendingResponses: feedbacks.filter(fb => fb.responses.length === 0).length
    };
    
    res.writeHead(200);
    res.end(JSON.stringify(stats));
  }
  else if (path.startsWith('/feedback/') && path.endsWith('/like') && method === 'POST') {
    const id = parseInt(path.split('/')[2]);
    const feedback = feedbacks.find(fb => fb.id === id);
    
    if (feedback) {
      feedback.likes++;
      res.writeHead(200);
      res.end(JSON.stringify(feedback));
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({ error: 'Feedback not found' }));
    }
  }
  else if (path.startsWith('/feedback/') && path.endsWith('/dislike') && method === 'POST') {
    const id = parseInt(path.split('/')[2]);
    const feedback = feedbacks.find(fb => fb.id === id);
    
    if (feedback) {
      feedback.dislikes++;
      res.writeHead(200);
      res.end(JSON.stringify(feedback));
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({ error: 'Feedback not found' }));
    }
  }
  else if (path.startsWith('/feedback/') && path.endsWith('/report') && method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify({ message: 'Report submitted successfully' }));
  }
  else if (path === '/health' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({ status: 'OK', message: 'Simple feedback server is running' }));
  }
  else {
    res.writeHead(404);
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

server.listen(port, () => {
  console.log(`🚀 Simple feedback server running on http://localhost:${port}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /feedback - Get all feedbacks`);
  console.log(`   POST /feedback - Create new feedback`);
  console.log(`   GET  /feedback/stats - Get feedback statistics`);
  console.log(`   POST /feedback/:id/like - Like a feedback`);
  console.log(`   POST /feedback/:id/dislike - Dislike a feedback`);
  console.log(`   POST /feedback/:id/report - Report a feedback`);
  console.log(`   GET  /health - Health check`);
  console.log(`\n✅ Server is ready to accept requests!`);
});

process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed gracefully');
    process.exit(0);
  });
});
