import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import { MailService } from './mail.service';

@Module({
  imports: [
    MailerModule.forRoot({
      transport: {
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {                                     //sender
          user: '<EMAIL>',
          pass: 'xvkf rxww cxxt lmec',
        },
      },
      defaults: {
        from: '"Mon App" <<EMAIL>>',
      },
    }),
  ],
  providers: [MailService],
  exports: [MailService],
})
export class MailModule {}
