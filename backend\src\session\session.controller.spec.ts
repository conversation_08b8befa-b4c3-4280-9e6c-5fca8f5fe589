import { Test, TestingModule } from '@nestjs/testing';
import { SessionsController } from './session.controller';
import { SessionsService } from './session.service';

describe('SessionController', () => {
  let controller: SessionsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SessionsController],
      providers: [SessionsService],
    }).compile();

    controller = module.get<SessionsController>(SessionsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
