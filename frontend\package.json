{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-data-grid": "^8.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "bootstrap": "^5.3.5", "bootstrap-icons": "^1.12.1", "date-fns": "^4.1.0", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "mdb-react-ui-kit": "^9.0.0", "react": "^18.0.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.0.0", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "react-scripts": "^5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"webpack": "^5.99.6", "webpack-cli": "^6.0.1"}}