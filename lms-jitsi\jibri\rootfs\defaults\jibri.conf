{{ $IGNORE_CERTIFICATE_ERRORS := .Env.IGNORE_CERTIFICATE_ERRORS | default "false" | toBool -}}
{{ $ENABLE_PROMETHEUS := .Env.JIBRI_ENABLE_PROMETHEUS | default "false" | toBool -}}
{{ $JIBRI_RECORDING_RESOLUTION := .Env.JIBRI_RECORDING_RESOLUTION | default "1280x720" -}}
{{ $JIBRI_RECORDING_VIDEO_ENCODE_PRESET_RECORDING := .Env.JIBRI_RECORDING_VIDEO_ENCODE_PRESET_RECORDING | default "ultrafast" -}}
{{ $JIBRI_RECORDING_VIDEO_ENCODE_PRESET_STREAMING := .Env.JIBRI_RECORDING_VIDEO_ENCODE_PRESET_STREAMING | default "veryfast" -}}
{{ $JIBRI_RECORDING_CONSTANT_RATE_FACTOR := .Env.JIB<PERSON>_RECORDING_CONSTANT_RATE_FACTOR | default 25 -}}
{{ $JIBRI_RECORDING_FRAMERATE := .Env.JIBRI_RECORDING_FRAMERATE | default 30 -}}
{{ $JIBRI_RECORDING_QUEUE_SIZE := .Env.JIBRI_RECORDING_QUEUE_SIZE | default 4096 -}}
{{ $JIBRI_RECORDING_STREAMING_MAX_BITRATE := .Env.JIBRI_RECORDING_STREAMING_MAX_BITRATE | default "2976k" -}}
{{ $JIBRI_SINGLE_USE_MODE := .Env.JIBRI_SINGLE_USE_MODE | default "false" -}}
{{ $STATSD_HOST := .Env.JIBRI_STATSD_HOST | default "localhost" -}}
{{ $STATSD_PORT := .Env.JIBRI_STATSD_PORT | default "8125" -}}

jibri {
    // A unique identifier for this Jibri
    id = "{{ .Env.JIBRI_INSTANCE_ID }}"
    // Whether or not Jibri should return to idle state after handling
    // (successfully or unsuccessfully) a request.  A value of 'true'
    // here means that a Jibri will NOT return back to the IDLE state
    // and will need to be restarted in order to be used again.
    single-use-mode = {{ $JIBRI_SINGLE_USE_MODE }}

    api {
      {{ if or .Env.JIBRI_HTTP_API_EXTERNAL_PORT .Env.JIBRI_HTTP_API_INTERNAL_PORT -}}
      http {
        {{ if .Env.JIBRI_HTTP_API_EXTERNAL_PORT -}}
        external-api-port = {{ .Env.JIBRI_HTTP_API_EXTERNAL_PORT }}
        {{ end -}}
        {{ if .Env.JIBRI_HTTP_API_INTERNAL_PORT -}}
        internal-api-port = {{ .Env.JIBRI_HTTP_API_INTERNAL_PORT }}
        {{ end -}}
      }
      {{ end -}}
    }
    recording {
      recordings-directory = "{{ .Env.JIBRI_RECORDING_DIR | default "/config/recordings" }}"
      {{ if .Env.JIBRI_FINALIZE_RECORDING_SCRIPT_PATH -}}
      finalize-script = "{{ .Env.JIBRI_FINALIZE_RECORDING_SCRIPT_PATH }}"
      {{ end -}}
    }
{{ if .Env.JIBRI_WEBHOOK_SUBSCRIBERS -}}
    webhook {
      subscribers = [{{ range $index, $element := (splitList "," .Env.JIBRI_WEBHOOK_SUBSCRIBERS | compact) }}{{ if gt $index 0}},{{ end }}"{{ $element }}"{{ end }}]
    }{{ end }}
    ffmpeg {
      resolution =  "{{ $JIBRI_RECORDING_RESOLUTION }}"
      // The audio source that will be used to capture audio on Linux
      audio-source = "pulse"
      // The audio device that will be used to capture audio on Linux
      audio-device =  "default"
      framerate = {{ $JIBRI_RECORDING_FRAMERATE }}
      queue-size = {{ $JIBRI_RECORDING_QUEUE_SIZE }}
      streaming-max-bitrate = "{{ $JIBRI_RECORDING_STREAMING_MAX_BITRATE }}"
      // Available presets: ultrafast, superfast, veryfast, faster, fast, medium,
      // slow, slower, veryslow, placebo
      video-encode-preset-streaming = "{{ $JIBRI_RECORDING_VIDEO_ENCODE_PRESET_STREAMING }}"
      video-encode-preset-recording = "{{ $JIBRI_RECORDING_VIDEO_ENCODE_PRESET_RECORDING }}"

      // The range of the CRF scale is 0-51, where 0 is lossless,
      // 23 is the default, and 51 is worst quality possible.
      h264-constant-rate-factor = {{ $JIBRI_RECORDING_CONSTANT_RATE_FACTOR }}
    }

    {{ if .Env.CHROMIUM_FLAGS -}}
    chrome {
      // The flags which will be passed to chromium when launching
      flags = [
        "{{ join "\",\"" (splitList "," .Env.CHROMIUM_FLAGS | compact) }}"
      ]
    }
    {{ else if $IGNORE_CERTIFICATE_ERRORS -}}
    chrome {
      flags = [
        "--use-fake-ui-for-media-stream",
        "--start-maximized",
        "--kiosk",
        "--enabled",
        "--autoplay-policy=no-user-gesture-required",
        "--ignore-certificate-errors"
      ]
    }
    {{ end -}}

    stats {
    {{- if .Env.ENABLE_STATS_D }}
      enable-stats-d = {{ .Env.ENABLE_STATS_D }}
      host = "{{ $STATSD_HOST }}"
      port = {{ $STATSD_PORT }}
    {{- end }}
      prometheus.enabled = {{ $ENABLE_PROMETHEUS }}
    }
}

include "xmpp.conf"

include "/config/custom-jibri.conf"
