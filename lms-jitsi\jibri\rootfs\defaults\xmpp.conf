{{ $JIBRI_BREWERY_MUC := .Env.JIBRI_BREWERY_MUC | default "jibribrewery" -}}
{{ $XMPP_MUC_DOMAIN := .Env.XMPP_MUC_DOMAIN | default "muc.meet.jitsi" -}}
{{ $XMPP_MUC_DOMAIN_PREFIX := (split "." $XMPP_MUC_DOMAIN)._0  -}}
{{ $JIBRI_STRIP_DOMAIN_JID := .Env.JIBRI_STRIP_DOMAIN_JID | default $XMPP_MUC_DOMAIN_PREFIX -}}
{{ $JIBRI_RECORDER_USER := .Env.JIBRI_RECORDER_USER | default "recorder" -}}
{{ $JIBRI_USAGE_TIMEOUT := .Env.JIBRI_USAGE_TIMEOUT | default "0" -}}
{{ $JIBRI_XMPP_USER := .Env.JIBRI_XMPP_USER | default "jibri" -}}
{{ $XMPP_AUTH_DOMAIN := .Env.XMPP_AUTH_DOMAIN | default "auth.meet.jitsi" -}}
{{ $XMPP_DOMAIN := .Env.XMPP_DOMAIN | default "meet.jitsi" -}}
{{ $XMPP_INTERNAL_MUC_DOMAIN := .Env.XMPP_INTERNAL_MUC_DOMAIN | default "internal-muc.meet.jitsi" -}}
{{ $XMPP_HIDDEN_DOMAIN := .Env.XMPP_HIDDEN_DOMAIN | default "hidden.meet.jitsi" -}}
{{ $XMPP_TRUST_ALL_CERTS := .Env.XMPP_TRUST_ALL_CERTS | default "true" | toBool -}}
{{ $XMPP_PORT := .Env.XMPP_PORT | default "5222" -}}
{{ $XMPP_SERVER := .Env.XMPP_SERVER | default "xmpp.meet.jitsi" -}}
{{ $XMPP_SERVERS := splitList "," $XMPP_SERVER | compact -}}
{{/* assign env from context, preserve during range when . is re-assigned */}}
{{ $ENV := .Env -}}

jibri.api.xmpp.environments = [
    // See example_xmpp_envs.conf for an example of what is expected here
{{ range $index, $element := $XMPP_SERVERS -}}
{{ $SERVER := splitn ":" 2 $element }}
            {
                // A user-friendly name for this environment
                name = "{{ $ENV.XMPP_ENV_NAME }}-{{$index}}"

                // A list of XMPP server hosts to which we'll connect
                xmpp-server-hosts = [
                    "{{ $SERVER._0 }}"
                ]

                // The base XMPP domain
                xmpp-domain = "{{ $XMPP_DOMAIN }}"

                {{ if $ENV.PUBLIC_URL -}}
                // An (optional) base url the Jibri will join if it is set
                base-url = "{{ $ENV.PUBLIC_URL }}"
                {{ end -}}

                // The MUC we'll join to announce our presence for
                // recording and streaming services
                control-muc {
                    domain = "{{ $XMPP_INTERNAL_MUC_DOMAIN }}"
                    room-name = "{{ $JIBRI_BREWERY_MUC }}"
                    nickname = "{{ $ENV.JIBRI_INSTANCE_ID }}"
                }

                // The login information for the control MUC
                control-login {
                    domain = "{{ $XMPP_AUTH_DOMAIN }}"
                    port = "{{ $SERVER._1 | default $XMPP_PORT }}"
                    username = "{{ $JIBRI_XMPP_USER }}"
                    password = "{{ $ENV.JIBRI_XMPP_PASSWORD }}"
                }

                // The login information the selenium web client will use
                call-login {
                    domain = "{{ $XMPP_HIDDEN_DOMAIN }}"
                    username = "{{ $JIBRI_RECORDER_USER }}"
                    password = "{{ $ENV.JIBRI_RECORDER_PASSWORD }}"
                }

                // The value we'll strip from the room JID domain to derive
                // the call URL
                strip-from-room-domain = "{{ $JIBRI_STRIP_DOMAIN_JID }}."

                // How long Jibri sessions will be allowed to last before
                // they are stopped.  A value of 0 allows them to go on
                // indefinitely
                usage-timeout = "{{ $JIBRI_USAGE_TIMEOUT }}"

                // Whether or not we'll automatically trust any cert on
                // this XMPP domain
                trust-all-xmpp-certs = {{ $XMPP_TRUST_ALL_CERTS }}
            }
{{ end }}
]
