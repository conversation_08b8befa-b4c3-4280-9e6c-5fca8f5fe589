{{ $ENABLE_AUTH := .Env.ENABLE_AUTH | default "0" -}}
{{ $ENABLE_VISITORS := .Env.ENABLE_VISITORS | default "0" | toBool -}}
{{ $JICOFO_ENABLE_AUTH := .Env.JICOFO_ENABLE_AUTH | default $ENABLE_AUTH | toBool -}}
{{ $AUTH_TYPE := .Env.AUTH_TYPE | default "internal" -}}
{{ $JICOFO_AUTH_TYPE := .Env.JICOFO_AUTH_TYPE | default $AUTH_TYPE -}}
{{ $JICOFO_AUTH_LIFETIME := .Env.JICOFO_AUTH_LIFETIME | default "24 hours" -}}
{{ $ENABLE_SCTP := .Env.ENABLE_SCTP | default "1" | toBool -}}
{{ $ENABLE_RECORDING := .Env.ENABLE_RECORDING | default "0" | toBool -}}
{{ $ENABLE_TRANSCRIPTIONS := .Env.ENABLE_TRANSCRIPTIONS | default "0" | toBool -}}
{{ $ENABLE_OCTO := .Env.ENABLE_OCTO | default "0" | toBool -}}
{{ $ENABLE_OCTO_SCTP := .Env.ENABLE_OCTO_SCTP | default $ENABLE_SCTP | toBool -}}
{{ $ENABLE_AUTO_LOGIN := .Env.ENABLE_AUTO_LOGIN | default "1" | toBool -}}
{{ $ENABLE_REST := .Env.JICOFO_ENABLE_REST | default "0" | toBool -}}
{{ $ENABLE_JVB_XMPP_SERVER := .Env.ENABLE_JVB_XMPP_SERVER | default "0" | toBool -}}
{{ $ENABLE_SHARED_DOCUMENT_RANDOM_NAME := .Env.ENABLE_SHARED_DOCUMENT_RANDOM_NAME | default "0" | toBool -}}
{{ $HEALTH_CHECKS_USE_PRESENCE := .Env.JICOFO_HEALTH_CHECKS_USE_PRESENCE | default "0" | toBool -}}
{{ $ICE_FAILURE_INTERVAL := .Env.JICOFO_ICE_FAILURE_INTERVAL | default "60" -}}
{{ $ICE_FAILURE_MIN_ENDPOINTS := .Env.JICOFO_ICE_FAILURE_MIN_ENDPOINTS | default "40" -}}
{{ $ICE_FAILURE_THRESHOLD := .Env.JICOFO_ICE_FAILURE_THRESHOLD | default "0.1" -}}
{{ $JIBRI_BREWERY_MUC := .Env.JIBRI_BREWERY_MUC | default "jibribrewery" -}}
{{ $JIGASI_BREWERY_MUC := .Env.JIGASI_BREWERY_MUC | default "jigasibrewery" -}}
{{ $JVB_BREWERY_MUC := .Env.JVB_BREWERY_MUC | default "jvbbrewery" -}}
{{ $JIBRI_PENDING_TIMEOUT := .Env.JIBRI_PENDING_TIMEOUT | default "90 seconds" -}}
{{ $JVB_XMPP_AUTH_DOMAIN := .Env.JVB_XMPP_AUTH_DOMAIN | default "auth.jvb.meet.jitsi" -}}
{{ $JVB_XMPP_INTERNAL_MUC_DOMAIN := .Env.JVB_XMPP_INTERNAL_MUC_DOMAIN | default "muc.jvb.meet.jitsi" -}}
{{ $JVB_XMPP_PORT := .Env.JVB_XMPP_PORT | default "6222" -}}
{{ $JVB_XMPP_SERVER := .Env.JVB_XMPP_SERVER | default "xmpp.jvb.meet.jitsi" -}}
{{ $RESTART_REQUEST_INTERVAL := .Env.JICOFO_RESTART_REQUEST_INTERVAL | default "60" -}}
{{ $VISITORS_MAX_VISITORS_PER_NODE := .Env.VISITORS_MAX_VISITORS_PER_NODE | default "250" }}
{{ $VISITORS_MUC_PREFIX := .Env.PROSODY_VISITORS_MUC_PREFIX | default "muc" -}}
{{ $VISITORS_REQUIRE_MUC_CONFIG := .Env.JICOFO_VISITORS_REQUIRE_MUC_CONFIG | default "0" | toBool }}
{{ $VISITORS_XMPP_AUTH_DOMAIN := .Env.VISITORS_XMPP_AUTH_DOMAIN | default "auth.meet.jitsi" -}}
{{ $VISITORS_XMPP_DOMAIN := .Env.VISITORS_XMPP_DOMAIN | default "meet.jitsi" -}}
{{ $VISITORS_XMPP_SERVER := .Env.VISITORS_XMPP_SERVER | default "" -}}
{{ $VISITORS_XMPP_SERVERS := splitList "," $VISITORS_XMPP_SERVER | compact -}}
{{ $VISITORS_XMPP_PORT := .Env.VISITORS_XMPP_PORT | default 52220 }}
{{ $XMPP_AUTH_DOMAIN := .Env.XMPP_AUTH_DOMAIN | default "auth.meet.jitsi" -}}
{{ $XMPP_MUC_DOMAIN := .Env.XMPP_MUC_DOMAIN | default "muc.meet.jitsi" -}}
{{ $XMPP_INTERNAL_MUC_DOMAIN := .Env.XMPP_INTERNAL_MUC_DOMAIN | default "internal-muc.meet.jitsi" -}}
{{ $XMPP_DOMAIN := .Env.XMPP_DOMAIN | default "meet.jitsi" -}}
{{ $XMPP_HIDDEN_DOMAIN := .Env.XMPP_HIDDEN_DOMAIN | default "hidden.meet.jitsi" -}}
{{ $XMPP_PORT := .Env.XMPP_PORT | default "5222" -}}
{{ $XMPP_SERVER := .Env.XMPP_SERVER | default "xmpp.meet.jitsi" -}}
{{ $MAX_SSRCS_PER_USER := .Env.MAX_SSRCS_PER_USER | default "20" -}}
{{ $MAX_SSRC_GROUPS_PER_USER := .Env.MAX_SSRC_GROUPS_PER_USER | default $MAX_SSRCS_PER_USER -}}
{{ $TRUSTED_DOMAIN_LIST := .Env.JICOFO_TRUSTED_DOMAINS | default (or $ENABLE_RECORDING $ENABLE_TRANSCRIPTIONS | ternary $XMPP_HIDDEN_DOMAIN "") -}}
{{ $TRUSTED_DOMAINS := splitList "," $TRUSTED_DOMAIN_LIST | compact -}}
{{ $ENV := .Env }}

jicofo {
    {{ if $JICOFO_ENABLE_AUTH }}
    authentication {
      enabled = true
      // The type of authentication. Supported values are XMPP or JWT.
      {{ if eq $JICOFO_AUTH_TYPE "jwt" }}
      type = JWT
      {{ else }}
      type = XMPP
      {{ end }}
      login-url = "{{ $XMPP_DOMAIN }}"
      enable-auto-login = {{ $ENABLE_AUTO_LOGIN }}
      authentication-lifetime = {{ $JICOFO_AUTH_LIFETIME }}
    }
    {{ end }}

    // Configuration related to jitsi-videobridge
    bridge {
      {{ if .Env.MAX_BRIDGE_PARTICIPANTS }}
      max-bridge-participants = "{{ .Env.MAX_BRIDGE_PARTICIPANTS }}"
      {{ end }}

      {{ if .Env.BRIDGE_AVG_PARTICIPANT_STRESS }}
      // The assumed average stress per participant. default is 0.01
      average-participant-stress = "{{ .Env.BRIDGE_AVG_PARTICIPANT_STRESS }}"
      {{ end }}

      {{ if .Env.BRIDGE_STRESS_THRESHOLD }}
      // The stress level above which a bridge is considered overstressed. 0.8 is the default value
      stress-threshold = "{{ .Env.BRIDGE_STRESS_THRESHOLD }}"
      {{ end }}

      {{ if $ENABLE_VISITORS }}
      selection-strategy = VisitorSelectionStrategy
      visitor-selection-strategy = RegionBasedBridgeSelectionStrategy
      participant-selection-strategy = RegionBasedBridgeSelectionStrategy
      topology-strategy = VisitorTopologyStrategy
      {{ else }}
      {{ if .Env.OCTO_BRIDGE_SELECTION_STRATEGY }}
      selection-strategy = "{{ .Env.OCTO_BRIDGE_SELECTION_STRATEGY }}"
      {{ end }}
      {{ end }}

      {{ if .Env.JICOFO_ENABLE_BRIDGE_HEALTH_CHECKS }}
      health-checks {
        enabled = {{ .Env.JICOFO_ENABLE_BRIDGE_HEALTH_CHECKS | toBool }}
        use-presence = {{ $HEALTH_CHECKS_USE_PRESENCE }}
      }
      {{ end }}

      {{ if .Env.JICOFO_ENABLE_ICE_FAILURE_DETECTION }}
      ice-failure-detection {
        enabled = {{ .Env.JICOFO_ENABLE_ICE_FAILURE_DETECTION | toBool }}
        interval = {{ $ICE_FAILURE_INTERVAL }} seconds
        min-endpoints = {{ $ICE_FAILURE_MIN_ENDPOINTS }}
        threshold = {{ $ICE_FAILURE_THRESHOLD }}
      }
      {{ end }}
      {{ if .Env.JICOFO_ENABLE_LOAD_REDISTRIBUTION }}
      load-redistribution {
        enabled = {{ .Env.JICOFO_ENABLE_LOAD_REDISTRIBUTION | toBool }}
      }
      {{ end }}

      {{ if $ENABLE_JVB_XMPP_SERVER }}
      brewery-jid = "{{ $JVB_BREWERY_MUC }}@{{ $JVB_XMPP_INTERNAL_MUC_DOMAIN }}"
      {{ else }}
      brewery-jid = "{{ $JVB_BREWERY_MUC }}@{{ $XMPP_INTERNAL_MUC_DOMAIN }}"
      {{ end }}

      {{ if .Env.JICOFO_BRIDGE_REGION_GROUPS }}
      region-groups = [{{ .Env.JICOFO_BRIDGE_REGION_GROUPS }}]
      {{ end }}
    }
    // Configure the codecs and RTP extensions to be used in the offer sent to clients.
    codec {
      video {
        {{ if .Env.ENABLE_CODEC_VP8 }}
        vp8 {
          enabled = {{ .Env.ENABLE_CODEC_VP8 | toBool }}
        }
        {{ end }}
        {{ if .Env.ENABLE_CODEC_VP9 }}
        vp9 {
          enabled = {{ .Env.ENABLE_CODEC_VP9 | toBool }}
        }
        {{ end }}
        {{ if .Env.ENABLE_CODEC_AV1 }}
        av1 {
          enabled = {{ .Env.ENABLE_CODEC_AV1 | toBool }}
        }
        {{ end }}
        {{ if .Env.ENABLE_CODEC_H264 }}
        h264 {
          enabled = {{ .Env.ENABLE_CODEC_H264 | toBool }}
        }
        {{ end }}
      }
      audio {
        {{ if .Env.ENABLE_CODEC_OPUS_RED }}
        opus {
          red {
            enabled = {{ .Env.ENABLE_CODEC_OPUS_RED | toBool }}
          }
        }
        {{ end }}
      }
      rtp-extensions {
        video-layers-allocation {
          enabled = {{ .Env.ENABLE_VLA | default "0" | toBool }}
        }
      }
    }

    conference {
      {{ if .Env.ENABLE_AUTO_OWNER }}
      enable-auto-owner = {{ .Env.ENABLE_AUTO_OWNER | toBool }}
      {{ end }}

      {{ if .Env.ENABLE_MODERATOR_CHECKS }}
      enable-moderator-checks = {{ .Env.ENABLE_MODERATOR_CHECKS | toBool }}
      {{ end }}

      {{ if .Env.JICOFO_CONF_INITIAL_PARTICIPANT_WAIT_TIMEOUT }}
      initial-timeout = "{{ .Env.JICOFO_CONF_INITIAL_PARTICIPANT_WAIT_TIMEOUT }}"
      {{ end }}

      max-ssrcs-per-user = "{{ $MAX_SSRCS_PER_USER }}"

      max-ssrc-groups-per-user = "{{ $MAX_SSRC_GROUPS_PER_USER }}"

      {{ if .Env.JICOFO_CONF_SINGLE_PARTICIPANT_TIMEOUT }}
      single-participant-timeout = "{{ .Env.JICOFO_CONF_SINGLE_PARTICIPANT_TIMEOUT }}"
      {{ end }}

      {{ if .Env.JICOFO_CONF_SOURCE_SIGNALING_DELAYS }}
      source-signaling-delays = {{ .Env.JICOFO_CONF_SOURCE_SIGNALING_DELAYS }}
      {{ end }}

      {{ if .Env.JICOFO_CONF_MAX_AUDIO_SENDERS }}
      max-audio-senders = {{ .Env.JICOFO_CONF_MAX_AUDIO_SENDERS }}
      {{ end }}

      {{ if .Env.JICOFO_CONF_MAX_VIDEO_SENDERS }}
      max-video-senders = {{ .Env.JICOFO_CONF_MAX_VIDEO_SENDERS }}
      {{ end }}

      {{ if .Env.JICOFO_CONF_STRIP_SIMULCAST }}
      strip-simulcast = {{ .Env.JICOFO_CONF_STRIP_SIMULCAST | toBool }}
      {{ end }}

      {{ if .Env.JICOFO_CONF_SSRC_REWRITING }}
      use-ssrc-rewriting = {{ .Env.JICOFO_CONF_SSRC_REWRITING | toBool }}
      {{ end }}

      {{ if .Env.JICOFO_MULTI_STREAM_BACKWARD_COMPAT }}
      enable-multi-stream-backward-compat = {{ .Env.JICOFO_MULTI_STREAM_BACKWARD_COMPAT | toBool }}
      {{ end }}

      {{ if $ENABLE_SHARED_DOCUMENT_RANDOM_NAME }}
      shared-document {
        use-random-name = {{ $ENABLE_SHARED_DOCUMENT_RANDOM_NAME }}
      }
      {{ end }}

      {{ if .Env.JICOFO_RESTART_REQUEST_MAX }}
      restart-request-rate-limits {
        max-requests = {{ .Env.JICOFO_RESTART_REQUEST_MAX }}
        interval = {{ $RESTART_REQUEST_INTERVAL }} seconds
      }
      {{ end }}
    }

    {{ if .Env.JICOFO_ENABLE_HEALTH_CHECKS }}
    // Configuration for the internal health checks performed by jicofo.
    health {
      // Whether to perform health checks.
      enabled = {{ .Env.JICOFO_ENABLE_HEALTH_CHECKS | toBool }}
    }
    {{ end }}

    {{ if $ENABLE_RECORDING }}
    jibri {
      brewery-jid = "{{ $JIBRI_BREWERY_MUC }}@{{ $XMPP_INTERNAL_MUC_DOMAIN }}"
      {{ if .Env.JIBRI_REQUEST_RETRIES }}
      num-retries = "{{ .Env.JIBRI_REQUEST_RETRIES }}"
      {{ end }}
      pending-timeout = "{{ $JIBRI_PENDING_TIMEOUT }}"
    }
    {{ end }}

    {{ if .Env.JIBRI_SIP_BREWERY_MUC }}
    jibri-sip {
      brewery-jid = "{{ .Env.JIBRI_SIP_BREWERY_MUC }}"
    }
    {{ end }}

    {{ if or .Env.JIGASI_SIP_URI $ENABLE_TRANSCRIPTIONS }}
    jigasi {
      brewery-jid = "{{ $JIGASI_BREWERY_MUC }}@{{ $XMPP_INTERNAL_MUC_DOMAIN }}"
    }
    {{ end }}

    {{ if .Env.JICOFO_OCTO_REGION }}
    local-region = "{{ .Env.JICOFO_OCTO_REGION }}"
    {{ end }}

    octo {
      // Whether or not to use Octo. Note that when enabled, its use will be determined by
      // $jicofo.bridge.selection-strategy. There's a corresponding flag in the JVB and these
      // two MUST be in sync (otherwise bridges will crash because they won't know how to
      // deal with octo channels).
      enabled = {{ $ENABLE_OCTO }}
      sctp-datachannels = {{ $ENABLE_OCTO_SCTP }}
    }

    {{ if $ENABLE_REST }}
    rest {
      host = "0.0.0.0"
    }
    {{ end }}

    sctp {
      enabled = {{ $ENABLE_SCTP }}
    }
{{ if $ENABLE_VISITORS }}
    visitors {
      enabled = true

      {{ if .Env.VISITORS_MAX_PARTICIPANTS }}
      max-participants = {{ .Env.VISITORS_MAX_PARTICIPANTS }}
      {{ end }}
      {{ if $VISITORS_MAX_VISITORS_PER_NODE }}
      max-visitors-per-node = {{ $VISITORS_MAX_VISITORS_PER_NODE }}
      {{ end }}
      require-muc-config-flag = {{ $VISITORS_REQUIRE_MUC_CONFIG }}
    }
{{ end }}
    xmpp {
      {{ if $ENABLE_VISITORS }}
      {{ if $.Env.VISITORS_XMPP_SERVER }}
      visitors {
        {{ range $index, $element := $VISITORS_XMPP_SERVERS -}}
        {{ $SERVER := splitn ":" 2 $element }}
            v{{ $index }} {
                enabled = true
                conference-service = {{ $VISITORS_MUC_PREFIX }}.v{{ $index }}.{{ $VISITORS_XMPP_DOMAIN }}
                hostname = {{ $SERVER._0 }}
                {{ $DEFAULT_PORT := add $VISITORS_XMPP_PORT $index }}
                port = {{ $SERVER._1 | default $DEFAULT_PORT }}
                domain = "{{ $VISITORS_XMPP_AUTH_DOMAIN }}"
                xmpp-domain = v{{ $index }}.{{ $VISITORS_XMPP_DOMAIN }}
                password = "{{ $ENV.JICOFO_AUTH_PASSWORD }}"
                disable-certificate-verification = true
            }
        {{ end }}
      }
      {{ end }}
      {{ end }}
      client {
        enabled = true
        hostname = "{{ $XMPP_SERVER }}"
        port = "{{ $XMPP_PORT }}"
        domain = "{{ $XMPP_AUTH_DOMAIN }}"
        xmpp-domain = "{{ $XMPP_DOMAIN }}"
        username = "focus"
        password = "{{ .Env.JICOFO_AUTH_PASSWORD }}"
        conference-muc-jid = "{{ $XMPP_MUC_DOMAIN }}"
        client-proxy = "focus.{{ $XMPP_DOMAIN }}"
        disable-certificate-verification = true
      }
      {{ if $ENABLE_JVB_XMPP_SERVER }}
      service {
        enabled = true
        hostname = "{{ $JVB_XMPP_SERVER }}"
        port = "{{ $JVB_XMPP_PORT }}"
        domain =  "{{ $JVB_XMPP_AUTH_DOMAIN }}"
        username = "focus"
        password = "{{ .Env.JICOFO_AUTH_PASSWORD }}"
        disable-certificate-verification = true
      }
      {{ end }}

      trusted-domains = [ {{ range $index, $element := $TRUSTED_DOMAINS }}{{ if gt $index 0 }},{{ end }}"{{ $element }}"{{ end}} ]

    }
}

include "custom-jicofo.conf"
