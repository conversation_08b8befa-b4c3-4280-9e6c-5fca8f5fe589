{{ if .Env.SENTRY_DSN | toBool }}
handlers=java.util.logging.ConsoleHandler,io.sentry.jul.SentryHandler
{{ else }}
handlers= java.util.logging.ConsoleHandler
{{ end }}

java.util.logging.ConsoleHandler.level = ALL
java.util.logging.ConsoleHandler.formatter = org.jitsi.utils.logging2.JitsiLogFormatter
org.jitsi.utils.logging2.JitsiLogFormatter.programname=Jicofo

.level=INFO
io.sentry.jul.SentryHandler.level=WARNING

# Enable debug packets logging
#org.jitsi.impl.protocol.xmpp.level=FINE
