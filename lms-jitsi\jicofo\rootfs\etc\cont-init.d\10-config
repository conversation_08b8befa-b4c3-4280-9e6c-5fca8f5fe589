#!/usr/bin/with-contenv bash

export SENTRY_RELEASE="${SENTRY_RELEASE:-$(apt-cache policy jicofo | sed -n '/Installed/p' | sed -e 's/[^:]*: //')}"

if [[ -z $JICOFO_AUTH_PASSWORD ]]; then
    echo 'FATAL ERROR: Jicofo auth password must be set'
    exit 1
fi

OLD_JICOFO_AUTH_PASSWORD=passw0rd
if [[ "$JICOFO_AUTH_PASSWORD" == "$OLD_JICOFO_AUTH_PASSWORD" ]]; then
    echo 'FATAL ERROR: Jicofo auth password must be changed, check the README'
    exit 1
fi

# maintain backward compatibility with older variable
[ -z "${XMPP_HIDDEN_DOMAIN}" ] && export XMPP_HIDDEN_DOMAIN="$XMPP_RECORDER_DOMAIN"

tpl /defaults/logging.properties > /config/logging.properties
tpl /defaults/jicofo.conf > /config/jicofo.conf

chown -R jicofo:jitsi /config
