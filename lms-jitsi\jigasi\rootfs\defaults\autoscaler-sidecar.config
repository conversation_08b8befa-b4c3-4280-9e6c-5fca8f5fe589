{{ $SHUTDOWN_POLLING_INTERVAL := .Env.AUTOSCALER_SIDECAR_SHUTDOWN_POLLING_INTERVAL | default "60" -}}
{{ $STATS_POLLING_INTERVAL := .Env.AUTOSCALER_SIDECAR_STATS_POLLING_INTERVAL | default "30" -}}
{{ $JIGASI_STATS_PORT := .Env.JIGASI_STATS_PORT | default "8788" -}}
export SHUTDOWN_POLLING_INTERVAL={{ $SHUTDOWN_POLLING_INTERVAL }}
export STATS_POLLING_INTERVAL={{ $STATS_POLLING_INTERVAL }}
export PORT={{ .Env.AUTOSCALER_SIDECAR_PORT }}
export GRACEFUL_SHUTDOWN_SCRIPT="/usr/share/jigasi/graceful_shutdown.sh"
export TERMINATE_SCRIPT="/opt/jitsi/shutdown.sh"
export ENABLE_REPORT_STATS=true
export POLLING_URL="{{ .Env.AUTOSCALER_URL  }}/sidecar/poll"
export STATUS_URL="{{ .Env.AUTOSCALER_URL }}/sidecar/status"
export SHUTDOWN_URL="{{ .Env.AUTOSCALER_URL }}/sidecar/shutdown"
export STATS_RETRIEVE_URL="http://localhost:{{ $JIGASI_STATS_PORT }}/about/stats"
export STATS_REPORT_URL="{{ .Env.AUTOSCALER_URL }}/sidecar/stats"
export ASAP_SIGNING_KEY_FILE="{{ .Env.AUTOSCALER_SIDECAR_KEY_FILE }}"
export ASAP_JWT_KID="{{ .Env.AUTOSCALER_SIDECAR_KEY_ID }}"
export INSTANCE_TYPE="jigasi"
export INSTANCE_ID="{{ .Env.AUTOSCALER_SIDECAR_INSTANCE_ID }}"
export INSTANCE_METADATA='{"environment":"{{ .Env.XMPP_ENV_NAME }}","region":"{{ .Env.AUTOSCALER_SIDECAR_REGION }}","group":"{{ .Env.AUTOSCALER_SIDECAR_GROUP_NAME }}","name":"{{ .Env.JIGASI_INSTANCE_ID }}","version":"{{ .Env.JIGASI_VERSION }}","privateIp":"{{ .Env.LOCAL_ADDRESS }}","hostId":"{{ .Env.AUTOSCALER_SIDECAR_HOST_ID }}"}'
