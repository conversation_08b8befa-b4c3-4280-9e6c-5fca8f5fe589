{{ if .Env.SENTRY_DSN | toBool }}
handlers=java.util.logging.ConsoleHandler,io.sentry.jul.SentryHandler
{{ else }}
handlers=java.util.logging.ConsoleHandler
{{ end }}

java.util.logging.ConsoleHandler.level = ALL
java.util.logging.ConsoleHandler.formatter = org.jitsi.utils.logging2.JitsiLogFormatter

.level=INFO
net.sf.level=SEVERE
net.java.sip.communicator.plugin.reconnectplugin.level=FINE
org.ice4j.level=SEVERE
org.jitsi.impl.neomedia.level=SEVERE
io.sentry.jul.SentryHandler.level=WARNING

# Do not worry about missing strings
net.java.sip.communicator.service.resources.AbstractResourcesService.level=SEVERE

#net.java.sip.communicator.service.protocol.level=ALL
