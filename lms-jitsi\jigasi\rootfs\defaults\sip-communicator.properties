{{ $JIGASI_JVB_TIMEOUT := .Env.JIGASI_JVB_TIMEOUT | default "30000" -}}
{{ $JIGASI_LOCAL_REGION := .Env.JIGASI_LOCAL_REGION | default "" -}}
{{ $BOSH_URL_PATTERN := .Env.BOSH_URL_PATTERN | default "" -}}
{{ $XMPP_MUC_DOMAIN := .Env.XMPP_MUC_DOMAIN | default "muc.meet.jitsi" -}}
{{ $XMPP_SERVER := .Env.XMPP_SERVER | default "xmpp.meet.jitsi" -}}
{{ $XMPP_SERVERS := splitList "," $XMPP_SERVER | compact -}}
{{ $XMPP_PORT := .Env.XMPP_PORT | default "5222" -}}
{{ $XMPP_SERVER_ADDRESS := splitn ":" 3 ($XMPP_SERVERS | first) }}
{{ $SHUTDOWN_REST_ENABLED := .Env.SHUTDOWN_REST_ENABLED | default "false" | toBool -}}
{{ $TRUSTED_DOMAIN_LIST := .Env.JIGASI_TRUSTED_DOMAINS | default "" -}}
{{ $TRUSTED_DOMAINS := splitList "," $TRUSTED_DOMAIN_LIST | compact -}}

net.java.sip.communicator.impl.protocol.SingleCallInProgressPolicy.enabled=false

# Adjust opus encoder complexity
net.java.sip.communicator.impl.neomedia.codec.audio.opus.encoder.COMPLEXITY=10

# Disables packet logging
net.java.sip.communicator.packetlogging.PACKET_LOGGING_ENABLED=false

# Control REST Shutdown
org.jitsi.jigasi.ENABLE_REST_SHUTDOWN={{ $SHUTDOWN_REST_ENABLED }}

org.jitsi.jigasi.BREWERY_ENABLED=true
org.jitsi.jigasi.MUC_JOIN_TIMEOUT=10

org.jitsi.jigasi.HEALTH_CHECK_SIP_URI={{ .Env.JIGASI_HEALTH_CHECK_SIP_URI | default "" }}
org.jitsi.jigasi.HEALTH_CHECK_INTERVAL={{ .Env.JIGASI_HEALTH_CHECK_INTERVAL | default "300000" }}
org.jitsi.jigasi.HEALTH_CHECK_TIMEOUT=600000

org.jitsi.jigasi.xmpp.acc.IS_SERVER_OVERRIDDEN=true
org.jitsi.jigasi.xmpp.acc.SERVER_ADDRESS={{ $XMPP_SERVER_ADDRESS._0 }}
org.jitsi.jigasi.xmpp.acc.SERVER_PORT={{ $XMPP_SERVER_ADDRESS._1 | default $XMPP_PORT }}
org.jitsi.jigasi.xmpp.acc.VIDEO_CALLING_DISABLED=true
org.jitsi.jigasi.xmpp.acc.JINGLE_NODES_ENABLED=false
org.jitsi.jigasi.xmpp.acc.AUTO_DISCOVER_STUN=false
org.jitsi.jigasi.xmpp.acc.USE_DEFAULT_STUN_SERVER=false
org.jitsi.jigasi.xmpp.acc.IM_DISABLED=true
org.jitsi.jigasi.xmpp.acc.SERVER_STORED_INFO_DISABLED=true
org.jitsi.jigasi.xmpp.acc.IS_FILE_TRANSFER_DISABLED=true

# Activate this property if you are using self-signed certificates or other
# type of non-trusted certicates. In this mode your service trust in the
# remote certificates always.
net.java.sip.communicator.service.gui.ALWAYS_TRUST_MODE_ENABLED=true


{{ if .Env.JIGASI_SIP_DEFAULT_ROOM }}
org.jitsi.jigasi.DEFAULT_JVB_ROOM_NAME={{ .Env.JIGASI_SIP_DEFAULT_ROOM }}
{{ end }}

org.jitsi.jigasi.MUC_SERVICE_ADDRESS={{ $XMPP_MUC_DOMAIN }}

# when checking other participants whether they are jibri/jigasi we can also check the the domain they use for connecting
{{ if $TRUSTED_DOMAIN_LIST }}
org.jitsi.jigasi.TRUSTED_DOMAINS=[ {{ range $index, $element := $TRUSTED_DOMAINS }}{{ if gt $index 0 }},{{ end }}"{{ $element }}"{{ end}} ]
{{ end }}

org.jitsi.jigasi.JVB_INVITE_TIMEOUT={{ $JIGASI_JVB_TIMEOUT }}

{{ if $JIGASI_LOCAL_REGION }}
org.jitsi.jigasi.LOCAL_REGION={{ $JIGASI_LOCAL_REGION }}
{{ end }}

{{ if $BOSH_URL_PATTERN }}
org.jitsi.jigasi.xmpp.acc.BOSH_URL_PATTERN={{ $BOSH_URL_PATTERN }}
{{ end }}

{{ if .Env.JIGASI_STATS_ID -}}
org.jitsi.jigasi.STATS_ID={{ .Env.JIGASI_STATS_ID }}
{{- end }}

{{ if .Env.JIGASI_CONFIGURATION -}}
{{ join "\n" (splitList "," .Env.JIGASI_CONFIGURATION | compact) }}
{{ end -}}
