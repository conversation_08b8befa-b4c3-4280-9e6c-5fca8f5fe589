{{ $ENABLE_VISITORS := .Env.ENABLE_VISITORS | default "0" | toBool -}}
{{ $JIGASI_SIP_PASSWORD := .Env.JIGASI_SIP_PASSWORD | default "replaceme" -}}
{{ $JIGASI_XMPP_USER := .Env.JIGASI_XMPP_USER | default "jigasi" -}}
{{ $XMPP_AUTH_DOMAIN := .Env.XMPP_AUTH_DOMAIN | default "auth.meet.jitsi" -}}
{{ $XMPP_DOMAIN := .Env.XMPP_DOMAIN | default "meet.jitsi" -}}
{{ $XMPP_GUEST_DOMAIN := .Env.XMPP_GUEST_DOMAIN | default "guest.meet.jitsi" -}}
{{ $USE_TRANSLATOR_IN_CONFERENCE := .Env.USE_TRANSLATOR_IN_CONFERENCE | default "0" | toBool -}}

org.jitsi.jigasi.ENABLE_SIP=true

# SIP account
net.java.sip.communicator.impl.protocol.sip.acc1=acc1
{{ if .Env.JIGASI_SIP_SERVER }}
net.java.sip.communicator.impl.protocol.sip.acc1.PROXY_ADDRESS={{ .Env.JIGASI_SIP_SERVER }}
net.java.sip.communicator.impl.protocol.sip.acc1.PROXY_AUTO_CONFIG=false
net.java.sip.communicator.impl.protocol.sip.acc1.PROXY_PORT={{ .Env.JIGASI_SIP_PORT | default "5060" }}
net.java.sip.communicator.impl.protocol.sip.acc1.PREFERRED_TRANSPORT={{ .Env.JIGASI_SIP_TRANSPORT | default "UDP" }}
{{ end }}
{{ if .Env.JIGASI_ENABLE_SDES_SRTP | default "0" | toBool }}
net.java.sip.communicator.impl.protocol.sip.acc1.SAVP_OPTION=1
net.java.sip.communicator.impl.protocol.sip.acc1.DEFAULT_ENCRYPTION=true
net.java.sip.communicator.impl.protocol.sip.acc1.DEFAULT_SIPZRTP_ATTRIBUTE=false
net.java.sip.communicator.impl.protocol.sip.acc1.ENCRYPTION_PROTOCOL.ZRTP=0
net.java.sip.communicator.impl.protocol.sip.acc1.ENCRYPTION_PROTOCOL.SDES=1
net.java.sip.communicator.impl.protocol.sip.acc1.ENCRYPTION_PROTOCOL.DTLS-SRTP=0
net.java.sip.communicator.impl.protocol.sip.acc1.ENCRYPTION_PROTOCOL_STATUS.ZRTP=false
net.java.sip.communicator.impl.protocol.sip.acc1.ENCRYPTION_PROTOCOL_STATUS.SDES=true
net.java.sip.communicator.impl.protocol.sip.acc1.ENCRYPTION_PROTOCOL_STATUS.DTLS-SRTP=false
net.java.sip.communicator.impl.protocol.sip.acc1.SDES_CIPHER_SUITES=AES_CM_128_HMAC_SHA1_80,AES_CM_128_HMAC_SHA1_32
{{ end }}
net.java.sip.communicator.impl.protocol.sip.acc1.ACCOUNT_UID=SIP\:{{ .Env.JIGASI_SIP_URI }}
net.java.sip.communicator.impl.protocol.sip.acc1.PASSWORD={{ $JIGASI_SIP_PASSWORD | b64enc }}
net.java.sip.communicator.impl.protocol.sip.acc1.PROTOCOL_NAME=SIP
net.java.sip.communicator.impl.protocol.sip.acc1.SERVER_ADDRESS={{ .Env.JIGASI_SIP_SERVER }}
net.java.sip.communicator.impl.protocol.sip.acc1.USER_ID={{ .Env.JIGASI_SIP_URI }}
net.java.sip.communicator.impl.protocol.sip.acc1.KEEP_ALIVE_INTERVAL=25
net.java.sip.communicator.impl.protocol.sip.acc1.KEEP_ALIVE_METHOD={{ .Env.JIGASI_SIP_KEEP_ALIVE_METHOD | default "OPTIONS" }}
net.java.sip.communicator.impl.protocol.sip.acc1.VOICEMAIL_ENABLED=false
net.java.sip.communicator.impl.protocol.sip.acc1.JITSI_MEET_ROOM_HEADER_NAME=X-Room-Name
net.java.sip.communicator.impl.protocol.sip.acc1.JITSI_MEET_DOMAIN_BASE_HEADER_NAME=X-Domain-Base
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.AMR-WB/16000=750
# Temporarily disable G722, see: https://github.com/jitsi/docker-jitsi-meet/issues/1357
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.G722/8000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.GSM/8000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.H263-1998/90000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.H264/90000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.PCMA/8000=600
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.PCMU/8000=650
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.SILK/12000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.SILK/16000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.SILK/24000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.SILK/8000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.VP8/90000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.iLBC/8000=10
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.opus/48000=1000
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.red/90000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.speex/16000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.speex/32000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.speex/8000=0
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.telephone-event/8000=1
net.java.sip.communicator.impl.protocol.sip.acc1.Encodings.ulpfec/90000=0
net.java.sip.communicator.impl.protocol.sip.acc1.OVERRIDE_ENCODINGS=true
net.java.sip.communicator.impl.protocol.sip.acc1.DOMAIN_BASE={{ $XMPP_DOMAIN }}

{{ if .Env.ENABLE_AUTH | default "0" | toBool }}
{{ if .Env.ENABLE_GUESTS | default "0" | toBool }}
org.jitsi.jigasi.xmpp.acc.USER_ID={{ $JIGASI_XMPP_USER }}@{{ $XMPP_GUEST_DOMAIN }}
org.jitsi.jigasi.xmpp.acc.ANONYMOUS_AUTH=true
{{ else }}
org.jitsi.jigasi.xmpp.acc.USER_ID={{ $JIGASI_XMPP_USER }}@{{ $XMPP_AUTH_DOMAIN }}
org.jitsi.jigasi.xmpp.acc.ANONYMOUS_AUTH=false
{{ end }}
org.jitsi.jigasi.xmpp.acc.PASS={{ .Env.JIGASI_XMPP_PASSWORD }}
org.jitsi.jigasi.xmpp.acc.ALLOW_NON_SECURE=true
{{ end }}

# forces sending presence on sip call hangup
net.java.sip.communicator.impl.protocol.jabber.FORCE_PRESENCE_ON_LEAVE=true

{{ if $USE_TRANSLATOR_IN_CONFERENCE }}
org.jitsi.jigasi.xmpp.acc.USE_TRANSLATOR_IN_CONFERENCE=true
net.java.sip.communicator.impl.protocol.sip.acc1.USE_TRANSLATOR_IN_CONFERENCE=true
# Should be enabled when using translator mode
net.java.sip.communicator.impl.neomedia.audioSystem.audiosilence.captureDevice_list=["AudioSilenceCaptureDevice:noTransferData"]
{{ end }}

{{ if $ENABLE_VISITORS -}}
org.jitsi.jigasi.ENABLE_SIP_VISITORS=true
{{ if .Env.JIGASI_VISITORS_QUEUE_SERVICE_URL -}}
org.jitsi.jigasi.VISITOR_QUEUE_SERVICE={{ .Env.JIGASI_VISITORS_QUEUE_SERVICE_URL }}
{{ end -}}
{{ if .Env.JIGASI_VISITORS_QUEUE_SERVICE_PRIVATE_KEY_PATH -}}
org.jitsi.jigasi.VISITOR_QUEUE_SERVICE_PRIVATE_KEY_PATH={{ .Env.JIGASI_VISITORS_QUEUE_SERVICE_PRIVATE_KEY_PATH }}
{{ end -}}
{{ if .Env.JIGASI_VISITORS_QUEUE_SERVICE_PRIVATE_KEY_ID -}}
org.jitsi.jigasi.VISITOR_QUEUE_SERVICE_PRIVATE_KEY_ID={{ .Env.JIGASI_VISITORS_QUEUE_SERVICE_PRIVATE_KEY_ID }}
{{ end -}}
{{ end -}}
