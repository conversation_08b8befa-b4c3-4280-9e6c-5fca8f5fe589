{{ $JIGASI_TRANSCRIBER_USER := .Env.JIGASI_TRANSCRIBER_USER | default "transcriber" -}}
{{ $JIGASI_TRANSCRIBER_FILTER_SILENCE := .Env.JIGASI_TRANSCRIBER_FILTER_SILENCE | default "0" | toBool -}}
{{ $XMPP_HIDDEN_DOMAIN := .Env.XMPP_HIDDEN_DOMAIN | default "hidden.meet.jitsi" -}}
{{ $JIGASI_TRANSCRIBER_ENABLE_SAVING := .Env.JIGASI_TRANSCRIBER_ENABLE_SAVING | default "1" | toBool -}}
{{ $JIGASI_TRANSCRIBER_ENABLE_TRANSLATION := .Env.JIGASI_TRANSCRIBER_ENABLE_TRANSLATION | default "0" | toBool -}}

org.jitsi.jigasi.ENABLE_SIP=false
org.jitsi.jigasi.xmpp.acc.USER_ID={{ $JIGASI_TRANSCRIBER_USER }}@{{ $XMPP_HIDDEN_DOMAIN }}
org.jitsi.jigasi.xmpp.acc.PASS={{ .Env.JIGASI_TRANSCRIBER_PASSWORD }}
org.jitsi.jigasi.xmpp.acc.ANONYMOUS_AUTH=false
org.jitsi.jigasi.xmpp.acc.ALLOW_NON_SECURE=true

# Transcription config
org.jitsi.jigasi.ENABLE_TRANSCRIPTION=true
{{ if $JIGASI_TRANSCRIBER_ENABLE_TRANSLATION -}}
org.jitsi.jigasi.transcription.ENABLE_TRANSLATION=true
{{ end -}}
{{ if $JIGASI_TRANSCRIBER_ENABLE_SAVING -}}
org.jitsi.jigasi.transcription.DIRECTORY=/tmp/transcripts
org.jitsi.jigasi.transcription.BASE_URL={{ .Env.PUBLIC_URL }}/transcripts
org.jitsi.jigasi.transcription.jetty.port=-1
org.jitsi.jigasi.transcription.ADVERTISE_URL={{ .Env.JIGASI_TRANSCRIBER_ADVERTISE_URL | default "false"}}
org.jitsi.jigasi.transcription.SAVE_JSON=false
org.jitsi.jigasi.transcription.SAVE_TXT=true
{{ end -}}
org.jitsi.jigasi.transcription.SEND_JSON=true
org.jitsi.jigasi.transcription.SEND_TXT={{ .Env.JIGASI_TRANSCRIBER_SEND_TXT | default "false"}}
org.jitsi.jigasi.transcription.RECORD_AUDIO={{ .Env.JIGASI_TRANSCRIBER_RECORD_AUDIO | default "false"}}
org.jitsi.jigasi.transcription.RECORD_AUDIO_FORMAT=wav
{{ if .Env.JIGASI_TRANSCRIBER_CUSTOM_SERVICE -}}
org.jitsi.jigasi.transcription.customService={{ .Env.JIGASI_TRANSCRIBER_CUSTOM_SERVICE }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_CUSTOM_TRANSLATION_SERVICE -}}
org.jitsi.jigasi.transcription.translationService={{ .Env.JIGASI_TRANSCRIBER_CUSTOM_TRANSLATION_SERVICE }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_LIBRETRANSLATE_URL -}}
org.jitsi.jigasi.transcription.libreTranslate.api_url={{ .Env.JIGASI_TRANSCRIBER_LIBRETRANSLATE_URL }}
{{ end -}}
{{ if $JIGASI_TRANSCRIBER_FILTER_SILENCE -}}
org.jitsi.jigasi.transcription.FILTER_SILENCE = true
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL -}}
org.jitsi.jigasi.transcription.remoteTranscriptionConfigUrl={{ .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL }}
{{ if .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL_KEY_PATH -}}
org.jitsi.jigasi.transcription.remoteTranscriptionConfigUrl.keyPath={{ .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL_KEY_PATH }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL_KEY_ID -}}
org.jitsi.jigasi.transcription.remoteTranscriptionConfigUrl.kid={{ .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL_KEY_ID }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL_AUD -}}
org.jitsi.jigasi.transcription.remoteTranscriptionConfigUrl.aud={{ .Env.JIGASI_TRANSCRIBER_REMOTE_CONFIG_URL_AUD }}
{{ end -}}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_OCI_REGION -}}
org.jitsi.jigasi.transcription.oci.websocketUrl=wss://realtime.aiservice.{{ .Env.JIGASI_TRANSCRIBER_OCI_REGION }}.oci.oraclecloud.com
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_OCI_COMPARTMENT -}}
org.jitsi.jigasi.transcription.oci.compartmentId={{ .Env.JIGASI_TRANSCRIBER_OCI_COMPARTMENT }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_WHISPER_URL -}}
org.jitsi.jigasi.transcription.whisper.websocket_url={{ .Env.JIGASI_TRANSCRIBER_WHISPER_URL }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_WHISPER_PRIVATE_KEY_NAME -}}
org.jitsi.jigasi.transcription.whisper.private_key_name={{ .Env.JIGASI_TRANSCRIBER_WHISPER_PRIVATE_KEY_NAME }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_WHISPER_PRIVATE_KEY -}}
org.jitsi.jigasi.transcription.whisper.private_key={{ .Env.JIGASI_TRANSCRIBER_WHISPER_PRIVATE_KEY }}
{{ end -}}
{{ if .Env.JIGASI_TRANSCRIBER_VOSK_URL -}}
org.jitsi.jigasi.transcription.vosk.websocket_url={{ .Env.JIGASI_TRANSCRIBER_VOSK_URL }}
{{ end -}}
