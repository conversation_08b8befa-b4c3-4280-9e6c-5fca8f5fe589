{{ $JIGASI_BREWERY_MUC := .Env.JIGASI_BREWERY_MUC | default "jigasibrewery" -}}
{{ $JIGASI_XMPP_USER := .Env.JIGASI_XMPP_USER | default "jigasi" -}}
{{ $JIGASI_XMPP_PASSWORD := .Env.JIGASI_XMPP_PASSWORD | default "replaceme" -}}
{{ $PUBLIC_URL_DOMAIN := .Env.PUBLIC_URL | default "https://localhost:8443" | trimPrefix "https://" | trimSuffix "/" -}}
{{ $XMPP_AUTH_DOMAIN := .Env.XMPP_AUTH_DOMAIN | default "auth.meet.jitsi" -}}
{{ $XMPP_DOMAIN := .Env.XMPP_DOMAIN | default "meet.jitsi" -}}
{{ $XMPP_INTERNAL_MUC_DOMAIN := .Env.XMPP_INTERNAL_MUC_DOMAIN | default "internal-muc.meet.jitsi" -}}
{{ $XMPP_PORT := .Env.XMPP_PORT | default "5222" -}}
{{ $XMPP_SERVER := .Env.XMPP_SERVER | default "xmpp.meet.jitsi" -}}
{{ $XMPP_SERVERS := splitList "," $XMPP_SERVER | compact -}}

# XMPP account used for control
{{ range $index, $element := $XMPP_SERVERS -}}
{{ $SERVER := splitn ":" 3 $element }}
{{ $ID := $SERVER._2 | default $index }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}=acc-{{ $ID }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.ACCOUNT_UID=Jabber:{{ $JIGASI_XMPP_USER }}@{{ $XMPP_AUTH_DOMAIN }}@{{ $ID }}
net.java.sip.communicator.plugin.reconnectplugin.ATLEAST_ONE_SUCCESSFUL_CONNECTION.Jabber\:{{ $JIGASI_XMPP_USER }}@{{ $XMPP_AUTH_DOMAIN }}=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.USER_ID={{ $JIGASI_XMPP_USER }}@{{ $XMPP_AUTH_DOMAIN }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_SERVER_OVERRIDDEN=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.SERVER_ADDRESS={{ $SERVER._0 }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.SERVER_PORT={{ $SERVER._1 | default $XMPP_PORT }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.ALLOW_NON_SECURE=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.PASSWORD={{ $JIGASI_XMPP_PASSWORD | b64enc }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.AUTO_GENERATE_RESOURCE=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.RESOURCE_PRIORITY=30
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.KEEP_ALIVE_METHOD=XEP-0199
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.KEEP_ALIVE_INTERVAL=20
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.CALLING_DISABLED=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.JINGLE_NODES_ENABLED=false
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_CARBON_DISABLED=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.DEFAULT_ENCRYPTION=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_USE_ICE=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_ACCOUNT_DISABLED=false
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_PREFERRED_PROTOCOL=false
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.AUTO_DISCOVER_JINGLE_NODES=false
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.PROTOCOL=Jabber
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_USE_UPNP=false
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IM_DISABLED=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.SERVER_STORED_INFO_DISABLED=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.IS_FILE_TRANSFER_DISABLED=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.USE_DEFAULT_STUN_SERVER=false
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.ENCRYPTION_PROTOCOL.DTLS-SRTP=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.ENCRYPTION_PROTOCOL_STATUS.DTLS-SRTP=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.VIDEO_CALLING_DISABLED=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.OVERRIDE_ENCODINGS=true
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.G722/8000=705
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.GSM/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.H263-1998/90000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.H264/90000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.PCMA/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.PCMU/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.SILK/12000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.SILK/16000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.SILK/24000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.SILK/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.VP8/90000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.iLBC/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.opus/48000=750
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.speex/16000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.speex/32000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.speex/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.Encodings.telephone-event/8000=0
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.BREWERY={{ $JIGASI_BREWERY_MUC }}@{{ $XMPP_INTERNAL_MUC_DOMAIN }}
net.java.sip.communicator.impl.protocol.jabber.acc-{{ $ID }}.DOMAIN_BASE={{ $PUBLIC_URL_DOMAIN }}
{{ end -}}

org.jitsi.jigasi.ALLOWED_JID={{ $JIGASI_BREWERY_MUC }}@{{ $XMPP_INTERNAL_MUC_DOMAIN }}
