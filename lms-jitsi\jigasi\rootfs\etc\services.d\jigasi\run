#!/usr/bin/with-contenv bash

JAVA_SYS_PROPS="-Djava.util.logging.config.file=/config/logging.properties"

DAEMON=/usr/share/jigasi/jigasi.sh
DAEMON_OPTS="--nocomponent=true --configdir=/ --configdirname=config --min-port=${JIGASI_PORT_MIN:-20000} --max-port=${JIGASI_PORT_MAX:-20050}"

JIGASI_CMD="JAVA_SYS_PROPS=\"$JAVA_SYS_PROPS\" exec $DAEMON $DAEMON_OPTS"
[ -n "$JIGASI_LOG_FILE" ] && JIGASI_CMD="$JIGASI_CMD 2>&1 | tee $JIGASI_LOG_FILE"

exec s6-setuidgid jigasi /bin/bash -c "$JIGASI_CMD"
