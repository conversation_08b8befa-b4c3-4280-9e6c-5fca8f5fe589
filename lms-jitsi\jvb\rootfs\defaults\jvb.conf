{{ $COLIBRI_REST_ENABLED := .Env.COLIBRI_REST_ENABLED | default "false" | toBool -}}
{{ $DISABLE_XMPP := .Env.JVB_DISABLE_XMPP | default "0" | toBool -}}
{{ $ENABLE_COLIBRI_WEBSOCKET := .Env.ENABLE_COLIBRI_WEBSOCKET | default "0" | toBool -}}
{{ $ENABLE_OCTO := .Env.ENABLE_OCTO | default "0" | toBool -}}
{{ $ENABLE_SCTP := .Env.ENABLE_SCTP | default "1" | toBool -}}
{{ $ENABLE_JVB_XMPP_SERVER := .Env.ENABLE_JVB_XMPP_SERVER | default "0" | toBool }}
{{ $JVB_DISABLE_STUN := .Env.JVB_DISABLE_STUN | default "0" | toBool -}}
{{ $JVB_STUN_SERVERS := .Env.JVB_STUN_SERVERS | default "meet-jit-si-turnrelay.jitsi.net:443" -}}
{{ $JVB_AUTH_USER := .Env.JVB_AUTH_USER | default "jvb" -}}
{{ $JVB_BREWERY_MUC := .Env.JVB_BREWERY_MUC | default "jvbbrewery" -}}
{{ $JVB_CC_TRUST_BWE := .Env.JVB_CC_TRUST_BWE | default "true" | toBool -}}
{{ $JVB_MUC_NICKNAME := .Env.JVB_MUC_NICKNAME | default .Env.HOSTNAME -}}
{{ $JVB_ADVERTISE_PRIVATE_CANDIDATES := .Env.JVB_ADVERTISE_PRIVATE_CANDIDATES | default "true" | toBool -}}
{{ $JVB_ADVERTISE_IPS := .Env.JVB_ADVERTISE_IPS | default "" -}}
{{ $JVB_IPS := splitList "," $JVB_ADVERTISE_IPS | compact -}}
{{ $JVB_REQUIRE_VALID_ADDRESS := .Env.JVB_REQUIRE_VALID_ADDRESS | default "0" | toBool -}}
{{ $JVB_XMPP_AUTH_DOMAIN := .Env.JVB_XMPP_AUTH_DOMAIN | default "auth.jvb.meet.jitsi" -}}
{{ $JVB_XMPP_INTERNAL_MUC_DOMAIN := .Env.JVB_XMPP_INTERNAL_MUC_DOMAIN | default "muc.jvb.meet.jitsi" -}}
{{ $JVB_XMPP_PORT := .Env.JVB_XMPP_PORT | default "6222" -}}
{{ $JVB_XMPP_SERVER := .Env.JVB_XMPP_SERVER | default "xmpp.jvb.meet.jitsi" -}}
{{ $JVB_XMPP_SERVERS := splitList "," $JVB_XMPP_SERVER | compact -}}
{{ $PUBLIC_URL_DOMAIN := .Env.PUBLIC_URL | default "https://localhost:8443" | trimPrefix "https://" | trimSuffix "/" -}}
{{ $SHUTDOWN_REST_ENABLED := .Env.SHUTDOWN_REST_ENABLED | default "false" | toBool -}}
{{ $USE_USRSCTP := .Env.JVB_USE_USRSCTP | default "false" | toBool -}}
{{ $WS_DOMAIN := .Env.JVB_WS_DOMAIN | default $PUBLIC_URL_DOMAIN -}}
{{ $WS_SERVER_ID := .Env.JVB_WS_SERVER_ID | default .Env.JVB_WS_SERVER_ID_FALLBACK | default "default" -}}
{{ $WS_TLS := .Env.JVB_WS_TLS | default "1" | toBool -}}
{{ $XMPP_AUTH_DOMAIN := .Env.XMPP_AUTH_DOMAIN | default "auth.meet.jitsi" -}}
{{ $XMPP_INTERNAL_MUC_DOMAIN := .Env.XMPP_INTERNAL_MUC_DOMAIN | default "internal-muc.meet.jitsi" -}}
{{ $XMPP_PORT := .Env.XMPP_PORT | default "5222" -}}
{{ $XMPP_SERVER := .Env.XMPP_SERVER | default "xmpp.meet.jitsi" -}}
{{ $XMPP_SERVERS := splitList "," $XMPP_SERVER | compact -}}
{{/* assign env from context, preserve during range when . is re-assigned */}}
{{ $ENV := .Env -}}

videobridge {
    cc {
        use-vla-target-bitrate = {{ .Env.ENABLE_VLA | default "0" | toBool }}
        trust-bwe = {{ $JVB_CC_TRUST_BWE }}
    }
    ice {
        udp {
            port = {{ .Env.JVB_PORT | default 10000 }}
        }
        advertise-private-candidates = {{ $JVB_ADVERTISE_PRIVATE_CANDIDATES }}
    }
    apis {
{{ if not $DISABLE_XMPP -}}
        xmpp-client {
            configs {
{{ if $ENABLE_JVB_XMPP_SERVER }}
{{ range $index, $element := $JVB_XMPP_SERVERS -}}
{{ $SERVER := splitn ":" 2 $element }}
                shard{{ $index }} {
                    HOSTNAME = "{{ $SERVER._0 }}"
                    PORT = "{{ $SERVER._1 | default $JVB_XMPP_PORT }}"
                    DOMAIN = "{{ $JVB_XMPP_AUTH_DOMAIN }}"
                    USERNAME = "{{ $JVB_AUTH_USER }}"
                    PASSWORD = "{{ $ENV.JVB_AUTH_PASSWORD }}"
                    MUC_JIDS = "{{ $JVB_BREWERY_MUC }}@{{ $JVB_XMPP_INTERNAL_MUC_DOMAIN }}"
                    MUC_NICKNAME = "{{ $JVB_MUC_NICKNAME }}"
                    DISABLE_CERTIFICATE_VERIFICATION = true
                }
{{ end -}}
{{ else }}
{{ range $index, $element := $XMPP_SERVERS -}}
{{ $SERVER := splitn ":" 2 $element }}
                shard{{ $index }} {
                    HOSTNAME = "{{ $SERVER._0 }}"
                    PORT = "{{ $SERVER._1 | default $XMPP_PORT }}"
                    DOMAIN = "{{ $XMPP_AUTH_DOMAIN }}"
                    USERNAME = "{{ $JVB_AUTH_USER }}"
                    PASSWORD = "{{ $ENV.JVB_AUTH_PASSWORD }}"
                    MUC_JIDS = "{{ $JVB_BREWERY_MUC }}@{{ $XMPP_INTERNAL_MUC_DOMAIN }}"
                    MUC_NICKNAME = "{{ $JVB_MUC_NICKNAME }}"
                    DISABLE_CERTIFICATE_VERIFICATION = true
                }
{{ end -}}
{{ end -}}
            }
        }
{{ end -}}
        rest {
            enabled = {{ $COLIBRI_REST_ENABLED }}
        }
    }
    rest {
        shutdown {
            enabled = {{ $SHUTDOWN_REST_ENABLED }}
        }
    }
    sctp {
      enabled = {{ $ENABLE_SCTP }}
      use-usrsctp = {{ $USE_USRSCTP }}
    }
    stats {
        enabled = true
    }
    websockets {
        enabled = {{ $ENABLE_COLIBRI_WEBSOCKET }}
        domain = "{{ $WS_DOMAIN }}"
        tls = {{ $WS_TLS }}
        server-id = "{{ $WS_SERVER_ID }}"
    }
    http-servers {
        private {
          host = 0.0.0.0
          send-server-version = false
        }
        public {
            host = 0.0.0.0
            port = 9090
            send-server-version = false
        }
    }
    health {
        require-valid-address = {{ $JVB_REQUIRE_VALID_ADDRESS }}
    }

    {{ if $ENABLE_OCTO -}}
    relay {
        enabled = true
        region = "{{ .Env.JVB_OCTO_REGION | default "europe" }}"
        relay-id = "{{ .Env.JVB_OCTO_RELAY_ID | default .Env.JVB_OCTO_BIND_ADDRESS }}"
    }
    {{ end -}}
}

ice4j {
    harvest {
        mapping {
            stun {
{{ if not $JVB_DISABLE_STUN -}}
                addresses = [ "{{ join "\",\"" (splitList "," $JVB_STUN_SERVERS) }}" ]
{{ else -}}
                enabled = false
{{ end -}}
            }
            static-mappings = [
{{ range $index, $element := $JVB_IPS -}}
                {
                    local-address = "{{ $ENV.LOCAL_ADDRESS }}"
                    public-address = "{{ $element }}"
                    name = "ip-{{ $index }}"
                },
{{ end -}}
            ]
        }
    }
}

include "custom-jvb.conf"
