{{ if .Env.SENTRY_DSN | toBool }}
handlers=java.util.logging.ConsoleHandler,io.sentry.jul.SentryHandler
{{ else }}
handlers= java.util.logging.ConsoleHandler
{{ end }}

java.util.logging.ConsoleHandler.level = ALL
java.util.logging.ConsoleHandler.formatter = org.jitsi.utils.logging2.JitsiLogFormatter
org.jitsi.utils.logging2.JitsiLogFormatter.programname=JVB

.level=INFO
io.sentry.jul.SentryHandler.level=WARNING
