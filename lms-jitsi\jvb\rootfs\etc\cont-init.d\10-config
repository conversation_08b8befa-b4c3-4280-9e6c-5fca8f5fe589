#!/usr/bin/with-contenv bash

if [[ -z $JVB_DISABLE_XMPP ]]; then
    if [[ -z $JVB_AUTH_PASSWORD ]]; then
        echo 'FATAL ERROR: JVB auth password must be set'
        exit 1
    fi

    OLD_JVB_AUTH_PASSWORD=passw0rd
    if [[ "$JVB_AUTH_PASSWORD" == "$OLD_JVB_AUTH_PASSWORD" ]]; then
        echo 'FATAL ERROR: JVB auth password must be changed, check the README'
        exit 1
    fi

    [ -z "${XMPP_SERVER}" ] && export XMPP_SERVER=xmpp.meet.jitsi

    # On environments like Swarm the IP address used by the default gateway need not be
    # the one used for inter-container traffic. Use that one for our fallback ID.
    XMPP_SERVER_IP=$(dig +short +search ${XMPP_SERVER})
    export JVB_WS_SERVER_ID_FALLBACK=$(ip route get ${XMPP_SERVER_IP} | grep -oP '(?<=src ).*' | awk '{ print $1 '})
fi

# Migration from DOCKER_HOST_ADDRESS to JVB_ADVERTISE_IPS
if [[ -z "${JVB_ADVERTISE_IPS}" ]]; then
    if [[ ! -z "${DOCKER_HOST_ADDRESS}" ]]; then
        echo "WARNING: DOCKER_HOST_ADDRESS is deprecated, migrate to JVB_ADVERTISE_IPS"
        export JVB_ADVERTISE_IPS=${DOCKER_HOST_ADDRESS}
    fi
fi

# Local IP for the ice4j mapping harvester.
export LOCAL_ADDRESS=$(ip route get 1 | grep -oP '(?<=src ).*' | awk '{ print $1 '})

export SENTRY_RELEASE="${SENTRY_RELEASE:-$(apt-cache policy jitsi-videobridge2 | sed -n '/Installed/p' | sed -e 's/[^:]*: //')}"

if [[ -f /config/custom-sip-communicator.properties ]]; then
    cat /config/custom-sip-communicator.properties > /config/sip-communicator.properties
fi

# set random jvb nickname for the instance if is not set
[ -z "${JVB_INSTANCE_ID}" ] && export JVB_INSTANCE_ID="jvb-$(date +%N)"

# check for AUTOSCALER_URL, AUTOSCALER_SIDECAR_KEY_FILE and AUTOSCALER_SIDECAR_KEY_ID as indicator that sidecar should be enabled
if [ -n "$AUTOSCALER_URL" ]; then
    if [ -z "$AUTOSCALER_SIDECAR_KEY_FILE" ]; then
        export AUTOSCALER_SIDECAR_KEY_FILE="/etc/jitsi/autoscaler-sidecar/asap.pem"
    fi
    if [ -z "$AUTOSCALER_SIDECAR_KEY_ID" ]; then
        # assume key id is equal to the base real path of the key file minus .pem
        export AUTOSCALER_SIDECAR_KEY_ID="$(basename "$(realpath "$AUTOSCALER_SIDECAR_KEY_FILE")" | tr -d '.pem')"
    fi

    if [ -f "$AUTOSCALER_SIDECAR_KEY_FILE" ]; then
        echo "AUTOSCALER_URL found, enabling autoscaler sidecar"

        export JVB_VERSION="$(dpkg -s jitsi-videobridge2 | grep Version | awk '{print $2}' | sed 's/..$//')"

        [ -z "$AUTOSCALER_SIDECAR_PORT" ] && export AUTOSCALER_SIDECAR_PORT="6000"
        [ -z "$JIBRI_WEBHOOK_SUBSCRIBERS" ] && export JIBRI_WEBHOOK_SUBSCRIBERS="http://localhost:$AUTOSCALER_SIDECAR_PORT/hook"
        [ -z "$AUTOSCALER_SIDECAR_INSTANCE_ID" ] && export AUTOSCALER_SIDECAR_INSTANCE_ID="$JVB_INSTANCE_ID"
        [ -z "$AUTOSCALER_SIDECAR_REGION" ] && export AUTOSCALER_SIDECAR_REGION="docker"
        [ -z "$AUTOSCALER_SIDECAR_GROUP_NAME" ] && export AUTOSCALER_SIDECAR_GROUP_NAME="docker-jvb"

        mkdir -p /etc/jitsi/autoscaler-sidecar
        tpl /defaults/autoscaler-sidecar.config > /etc/jitsi/autoscaler-sidecar/config
    else
        echo "No key file at $AUTOSCALER_SIDECAR_KEY_FILE, leaving autoscaler sidecar disabled"
    fi
else
    echo "No AUTOSCALER_URL defined, leaving autoscaler sidecar disabled"
fi

tpl /defaults/logging.properties > /config/logging.properties
tpl /defaults/jvb.conf > /config/jvb.conf

chown -R jvb:jitsi /config
