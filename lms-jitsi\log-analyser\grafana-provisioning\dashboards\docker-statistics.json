{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 0, "y": 0}, "id": 4, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "container_memory_percent_ratio * 100", "instant": false, "legendFormat": "Memory Usage (in %)", "range": true, "refId": "A"}], "title": "Memory Usage", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 7, "y": 0}, "id": 2, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "exemplar": false, "expr": "container_cpu_utilization_ratio * 100", "format": "time_series", "instant": false, "legendFormat": "CPU Utilization (in %)", "range": true, "refId": "A"}], "title": "CPU Utilization", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 10, "x": 14, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "sum(rate(container_blockio_io_service_bytes_recursive_total{operation=\"read\"}[5m])) ", "hide": false, "instant": false, "legendFormat": "Read Operation", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "sum(rate(container_blockio_io_service_bytes_recursive_total{operation=\"write\"}[5m])) ", "hide": false, "instant": false, "legendFormat": "Write Operation", "range": true, "refId": "C"}], "title": "Block IO in bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "rate(container_cpu_usage_kernelmode_nanoseconds_total[5m])", "instant": false, "legendFormat": "Kernel Mode", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "rate(container_cpu_usage_usermode_nanoseconds_total[5m])", "hide": false, "instant": false, "legendFormat": "User Mode", "range": true, "refId": "B"}], "title": "CPU usage in different modes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "(container_memory_usage_total_bytes / container_memory_usage_limit_bytes) * 100", "instant": false, "legendFormat": "Total Bytes Read/Written", "range": true, "refId": "A"}], "title": "Container memory usage vs limit", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "exemplar": false, "expr": "rate(container_network_io_usage_rx_bytes_total{interface=\"eth0\"}[5m])", "instant": false, "legendFormat": "Network bytes received", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "rate(container_network_io_usage_tx_bytes_total{interface=\"eth0\"}[5m])", "hide": false, "instant": false, "legendFormat": "Network bytes Sent", "range": true, "refId": "B"}], "title": "Network bytes sent and received", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "rate(container_network_io_usage_rx_dropped_total{interface=\"eth0\"}[5m])", "instant": false, "legendFormat": "Network Bytes Dropped in Receiving", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "d301145e-8c4e-4027-bf6e-43e81f095020"}, "editorMode": "code", "expr": "rate(container_network_io_usage_tx_dropped_total{interface=\"eth0\"}[5m])", "hide": false, "instant": false, "legendFormat": "Network Bytes dropped in Sending", "range": true, "refId": "B"}], "title": "Network Packets dropped", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 38, "tags": [], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Docker Statistics", "uid": "************************************", "version": 3, "weekStart": ""}