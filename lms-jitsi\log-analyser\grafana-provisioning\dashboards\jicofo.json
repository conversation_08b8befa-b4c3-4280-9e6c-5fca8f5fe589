{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 6, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "{exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-jicofo\"", "queryType": "range", "refId": "A"}], "title": "Jicofo Logs", "type": "logs"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"displayLabels": ["percent"], "legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false, "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "loki", "uid": "b8130a28-4867-4668-917d-539c93852857"}, "editorMode": "code", "expr": "sum by (attributes_level) (\n  rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-jicofo\"| line_format \"{{.log}}\" | logfmt | pattern \"[<_>] <_level>: <_>\"[5m])\n)", "legendFormat": "Level: {{attributes_level}}", "queryType": "range", "refId": "A"}], "title": "Jicofo Log Levels Pie Chart", "type": "piechart"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 1, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "valueMode": "color"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "b8130a28-4867-4668-917d-539c93852857"}, "editorMode": "code", "expr": "sum by (attributes_level, attributes_attrs_service) (\n  rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-jicofo\"| line_format \"{{.attributes_message}}\" | logfmt | pattern \"[<_>] <attributes_level>#<attributes_attrs_service>: <_>\"[5m]))", "legendFormat": "Level: {{attributes_level}}", "queryType": "range", "refId": "A"}], "title": "Jicofo Log Levels Bar Chart", "transformations": [], "type": "bargauge"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "editorMode": "code", "expr": "sum by (attributes_codefile) (\n    rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-jicofo\"| attributes_level=\"ERROR\" | line_format \"{{.attributes_message}}\" | logfmt | pattern \"[<_>] <attributes_level>#<attributes_attrs_service>: <_>\"[5m]))", "queryType": "range", "refId": "A"}], "title": "Jicofo Total Rate of ERROR Logs Aggregated by Code File", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "description": "This panel shows the number of conference requests over time.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "count_over_time({exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-jicofo\" |= \"Conference request\" [1m])", "queryType": "range", "refId": "A"}], "title": "Jicofo Number of Conference Requests", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "sum(count_over_time({exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-jicofo\" |~ \"Member left|Terminating|Removed participant\" [1m]))", "queryType": "range", "refId": "A"}], "title": "Jicofo Total Counts of Member Left, Terminating, Removed Participant", "type": "gauge"}], "refresh": "", "schemaVersion": 38, "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Jicofo Dashboard", "uid": "f2dcfe84-3c27-4b1d-8583-bc2c97a8d22d", "version": 20, "weekStart": ""}