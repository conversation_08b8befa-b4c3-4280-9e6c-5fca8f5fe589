{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"barRadius": 0, "barWidth": 1, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "b8130a28-4867-4668-917d-539c93852857"}, "editorMode": "code", "expr": "sum by (attributes_attrs_service) (\n  rate({exporter=\"OTLP\"} | json | line_format \"{{.attributes_message}}\" | logfmt | pattern \"[<_>] <_level>: <_>\"[5m]))", "legendFormat": "{{attributes_attrs_service}}", "queryType": "range", "refId": "A"}], "title": "Jicofo, Prosody, Jitsi Web and JVB Log Counts", "type": "barchart"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 13}, "id": 2, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "{exporter=\"OTLP\"} ", "queryType": "range", "refId": "A"}], "title": "Jitsi All Logs (Jicofo, Prosody, JVB, Web)", "type": "logs"}], "refresh": "", "schemaVersion": 38, "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Jitsi All Components Dashboard", "uid": "b75d666d-4537-45e2-94a1-2783f9362b65", "version": 14, "weekStart": ""}