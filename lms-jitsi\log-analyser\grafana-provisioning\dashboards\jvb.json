{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 3, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "{exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-jvb\"", "queryType": "range", "refId": "A"}], "title": "JVB Logs", "type": "logs"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"displayLabels": ["percent", "name"], "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "loki", "uid": "b8130a28-4867-4668-917d-539c93852857"}, "editorMode": "code", "expr": "sum by (attributes_level) (\n  rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-jvb\"| line_format \"{{.log}}\" | logfmt | pattern \"[<_>] <_level>: <_>\"[5m])\n)", "legendFormat": "Level: {{attributes_level}}", "queryType": "range", "refId": "A"}], "title": "JVB Log Levels Pie Chart", "type": "piechart"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 1, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "valueMode": "color"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "b8130a28-4867-4668-917d-539c93852857"}, "editorMode": "code", "expr": "sum by (attributes_level, attributes_attrs_service) (\n  rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-jvb\" | line_format \"{{.attributes_message}}\" | logfmt | pattern \"[<_>] <attributes_level>#<attributes_attrs_service>: <_>\"[5m]))", "legendFormat": "Level: {{attributes_level}}", "queryType": "range", "refId": "A"}], "title": "JVB Log Levels Bar Chart", "type": "bargauge"}], "refresh": "", "schemaVersion": 38, "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "JVB Dashboard", "uid": "d53a9efb-ca3b-4f47-af3a-9638de8a35fa", "version": 10, "weekStart": ""}