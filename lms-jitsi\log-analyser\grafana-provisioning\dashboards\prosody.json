{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 6, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "{exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-prosody\"", "queryType": "range", "refId": "A"}], "title": "Prosody Logs", "type": "logs"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 1, "options": {"displayLabels": ["percent"], "legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false, "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "sum by (attributes_level) (\n  rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-prosody\"| line_format \"{{.log}}\" | logfmt | pattern \"[<_>] <_level>: <_>\"[5m])\n)", "legendFormat": "Level: {{attributes_level}}", "queryType": "range", "refId": "A"}], "title": "Prosody Log Levels Pie Chart", "type": "piechart"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 7, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "valueMode": "color"}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "sum by (attributes_level, attributes_attrs_service) (\n  rate({exporter=\"OTLP\"} | json|attributes_attrs_service=\"jitsi-prosody\"| line_format \"{{.attributes_message}}\" | logfmt | pattern \"[<_>] <attributes_level>#<attributes_attrs_service>: <_>\"[5m]))", "legendFormat": "Level: {{attributes_level}}", "queryType": "range", "refId": "A"}], "title": "Prosody Log Levels Bar Chart", "type": "bargauge"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 70}, {"color": "red", "value": 85}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 5, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "sum(count_over_time({exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-prosody\" |~ \"Starting room\" [1m]))", "queryType": "range", "refId": "A"}], "title": "Prosody Total Number of Rooms Started", "type": "gauge"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 70}, {"color": "red", "value": 85}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 3, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "sum(count_over_time({exporter=\"OTLP\"} | json | attributes_attrs_service=\"jitsi-prosody\" |~ \"Client disconnected\" [1m]))", "queryType": "range", "refId": "A"}], "title": "Prosody Total Number of Clients Disconnected", "type": "gauge"}, {"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 70}, {"color": "red", "value": 85}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 2, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "10.2.0", "targets": [{"datasource": {"type": "loki", "uid": "a4bdfb3e-762a-46e5-a79f-2e7bbe88d444"}, "editorMode": "code", "expr": "sum(count_over_time({exporter=\"OTLP\"} | json |attributes_attrs_service=\"jitsi-prosody\" |~ \"Client connected\" [1m]))", "queryType": "range", "refId": "A"}], "title": "Prosody Total Number of Clients Connected", "type": "gauge"}], "refresh": "", "schemaVersion": 38, "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Prosody Dashboard", "uid": "fe2d57bc-b09b-4688-8037-f642047b0cfc", "version": 1, "weekStart": ""}