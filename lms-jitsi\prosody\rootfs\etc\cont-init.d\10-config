#!/usr/bin/with-contenv bash

if [[ ! -f /etc/saslauthd.conf ]] && [[ "$AUTH_TYPE" == "ldap" ]]; then
  tpl /defaults/saslauthd.conf > /etc/saslauthd.conf
  mkdir -pm777 /var/run/saslauthd
  adduser prosody sasl
  echo >> /etc/ldap/ldap.conf "TLS_REQCERT allow"
fi

PROSODY_CFG="/config/prosody.cfg.lua"

if [[ ! -d /config/data ]]; then
    mkdir -pm 750 /config/data
fi

if [[ "$(stat -c %U /config)" != "prosody" ]]; then
    chown -R prosody /config
fi

if [[ "$(stat -c %U /prosody-plugins)" != "prosody" ]]; then
    chown -R prosody /prosody-plugins
fi

if [[ "$(stat -c %U /prosody-plugins-custom)" != "prosody" ]]; then
    chown -R prosody /prosody-plugins-custom
fi

if [[ "$(stat -c %U /prosody-plugins-contrib)" != "prosody" ]]; then
    chown -R prosody /prosody-plugins-contrib
fi

mkdir /config/certs
cp -r /defaults/* /config

[ -z "$PROSODY_MODE" ] && export PROSODY_MODE="client"

if [[ "$PROSODY_MODE" == "visitors" ]]; then
    echo "Prosody visitor mode, using alternate config"
    PROSODY_SITE_CFG="visitors.cfg.lua"
    rm /config/conf.d/jitsi-meet.cfg.lua
    rm /config/conf.d/brewery.cfg.lua
    # force jicofo into auth domain for visitor-mode prosody
    [ -z "$XMPP_AUTH_DOMAIN" ] && XMPP_AUTH_DOMAIN="auth.meet.jitsi"
    export PROSODY_ADMINS="focus@$XMPP_AUTH_DOMAIN"
elif [[ "$PROSODY_MODE" == "brewery" ]]; then
    echo "Prosody brewery mode, using alternate config"
    PROSODY_SITE_CFG="brewery.cfg.lua"
    rm /config/conf.d/jitsi-meet.cfg.lua
    rm /config/conf.d/visitors.cfg.lua
    # force jicofo into auth domain for brewer prosody
    [ -z "$JVB_XMPP_AUTH_DOMAIN" ] && JVB_XMPP_AUTH_DOMAIN="auth.meet.jitsi"
    # ensure proper certs are generated
    export XMPP_AUTH_DOMAIN="$JVB_XMPP_AUTH_DOMAIN"
    # brewery mode requires C2S encryption
    export PROSODY_C2S_REQUIRE_ENCRYPTION="true"

    mkdir -p /config/rules.d
    tpl /defaults/rules.d/jvb_muc_presence_filter.pfw > /config/rules.d/jvb_muc_presence_filter.pfw
else
    echo "Prosody normal mode, using default config"
    PROSODY_SITE_CFG="jitsi-meet.cfg.lua"
    rm /config/conf.d/visitors.cfg.lua
    rm /config/conf.d/brewery.cfg.lua
fi
tpl /defaults/prosody.cfg.lua > $PROSODY_CFG
tpl /defaults/conf.d/$PROSODY_SITE_CFG > /config/conf.d/$PROSODY_SITE_CFG

if [[ -z $JICOFO_AUTH_PASSWORD ]]; then
    echo 'FATAL ERROR: Jicofo auth password must be set'
    exit 1
fi

# Defaults
[ -z "${JIBRI_RECORDER_USER}" ] && export JIBRI_RECORDER_USER=recorder
[ -z "${JIBRI_XMPP_USER}" ] && export JIBRI_XMPP_USER=jibri
[ -z "${JIGASI_XMPP_USER}" ] && export JIGASI_XMPP_USER=jigasi
[ -z "${JVB_AUTH_USER}" ] && export JVB_AUTH_USER=jvb
[ -z "${XMPP_DOMAIN}" ] && export XMPP_DOMAIN=meet.jitsi
[ -z "${XMPP_AUTH_DOMAIN}" ] && export XMPP_AUTH_DOMAIN=auth.meet.jitsi
# maintain backward compatibility with older variable
[ -z "${XMPP_HIDDEN_DOMAIN}" ] && export XMPP_HIDDEN_DOMAIN="$XMPP_RECORDER_DOMAIN"
[ -z "${XMPP_HIDDEN_DOMAIN}" ] && export XMPP_HIDDEN_DOMAIN=hidden.meet.jitsi

prosodyctl --config $PROSODY_CFG register focus $XMPP_AUTH_DOMAIN $JICOFO_AUTH_PASSWORD

# if we are in client mode, we need to subscribe the focus user to the focus component proxy
if [[ "$PROSODY_MODE" == "client" ]]; then
    prosodyctl --config $PROSODY_CFG mod_roster_command subscribe focus.$XMPP_DOMAIN focus@$XMPP_AUTH_DOMAIN
fi

if [[ -z $JVB_AUTH_PASSWORD ]]; then
    echo 'FATAL ERROR: JVB auth password must be set'
    exit 1
fi

OLD_JVB_AUTH_PASSWORD=passw0rd
if [[ "$JVB_AUTH_PASSWORD" == "$OLD_JVB_AUTH_PASSWORD" ]]; then
    echo 'FATAL ERROR: JVB auth password must be changed, check the README'
    exit 1
fi

# we see the next register command to hang from time to time, suspect it's a race with mod_roster_command
# Once this is released: https://issues.prosody.im/1908 we can remove this sleep and make sure prosody is running
# and then use 'prosodyctl shell user create' to add user live and 'prosodyctl shell roster' to modify their roster live.
sleep 1

prosodyctl --config $PROSODY_CFG register $JVB_AUTH_USER $XMPP_AUTH_DOMAIN $JVB_AUTH_PASSWORD

if [[ ! -z $JIBRI_XMPP_PASSWORD ]]; then
    OLD_JIBRI_XMPP_PASSWORD=passw0rd
    if [[ "$JIBRI_XMPP_PASSWORD" == "$OLD_JIBRI_XMPP_PASSWORD" ]]; then
        echo 'FATAL ERROR: Jibri auth password must be changed, check the README'
        exit 1
    fi
    prosodyctl --config $PROSODY_CFG register $JIBRI_XMPP_USER $XMPP_AUTH_DOMAIN $JIBRI_XMPP_PASSWORD
fi

if [[ "$PROSODY_MODE" == "client" ]]; then
    if [[ ! -z $JIBRI_RECORDER_PASSWORD ]]; then
        OLD_JIBRI_RECORDER_PASSWORD=passw0rd
        if [[ "$JIBRI_RECORDER_PASSWORD" == "$OLD_JIBRI_RECORDER_PASSWORD" ]]; then
            echo 'FATAL ERROR: Jibri recorder password must be changed, check the README'
            exit 1
        fi
        prosodyctl --config $PROSODY_CFG register $JIBRI_RECORDER_USER $XMPP_HIDDEN_DOMAIN $JIBRI_RECORDER_PASSWORD
    fi
    if [[ "$(echo "$ENABLE_TRANSCRIPTIONS" | tr '[:upper:]' '[:lower:]')" == "true" ]] || [[ "$ENABLE_TRANSCRIPTIONS" == "1" ]]; then
        if [[ ! -z $JIGASI_TRANSCRIBER_PASSWORD ]]; then
            [ -z "$JIGASI_TRANSCRIBER_USER" ] && JIGASI_TRANSCRIBER_USER="transcriber"
            prosodyctl --config $PROSODY_CFG register $JIGASI_TRANSCRIBER_USER $XMPP_HIDDEN_DOMAIN $JIGASI_TRANSCRIBER_PASSWORD
        fi
    fi
fi

if [[ ! -z $JIGASI_XMPP_PASSWORD ]]; then
    OLD_JIGASI_XMPP_PASSWORD=passw0rd
    if [[ "$JIGASI_XMPP_PASSWORD" == "$OLD_JIGASI_XMPP_PASSWORD" ]]; then
        echo 'FATAL ERROR: Jigasi auth password must be changed, check the README'
        exit 1
    fi
    prosodyctl --config $PROSODY_CFG register $JIGASI_XMPP_USER $XMPP_AUTH_DOMAIN $JIGASI_XMPP_PASSWORD
fi

if [[ "$PROSODY_MODE" == "visitors" ]]; then
    [ -z "$VISITORS_XMPP_DOMAIN" ] && VISITORS_XMPP_DOMAIN="meet.jitsi"
    [ -z "$PROSODY_VISITOR_INDEX" ] && PROSODY_VISITOR_INDEX=0
    FULL_VISITORS_XMPP_DOMAIN="v$PROSODY_VISITOR_INDEX.$VISITORS_XMPP_DOMAIN"
    if [[ ! -f /config/certs/$FULL_VISITORS_XMPP_DOMAIN.crt ]]; then
        # echo for using all default values
        echo | prosodyctl --config $PROSODY_CFG cert generate $FULL_VISITORS_XMPP_DOMAIN
    fi
elif [[ "$PROSODY_MODE" == "brewery" ]]; then
    echo "No need to generate certs for main XMPP domain in brewery mode"
else
    if [[ ! -f /config/certs/$XMPP_DOMAIN.crt ]]; then
        # echo for using all default values
        echo | prosodyctl --config $PROSODY_CFG cert generate $XMPP_DOMAIN
    fi
fi

if [[ ! -f /config/certs/$XMPP_AUTH_DOMAIN.crt ]]; then
    # echo for using all default values
    echo | prosodyctl --config $PROSODY_CFG cert generate $XMPP_AUTH_DOMAIN
fi

# certs will be created in /config/data
mv /config/data/*.{crt,key} /config/certs/ || true
rm -f /config/data/*.cnf
